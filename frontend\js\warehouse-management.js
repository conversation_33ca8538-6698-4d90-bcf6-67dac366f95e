/**
 * 仓库管理主要业务逻辑
 * 使用Vue.js 3 Composition API
 */

const { createApp, ref, reactive, computed, onMounted, watch, nextTick } = Vue;

// 仓库管理应用
const WarehouseApp = {
    setup() {
        // 响应式状态
        const loading = ref(false);
        const currentUser = ref(null);
        const activeTab = ref('inventory'); // 当前活动标签页
        const searchTerm = ref('');
        const selectedItems = ref([]);

        // 库存管理状态
        const inventory = reactive({
            list: [],
            pagination: {
                page: 1,
                limit: 20,
                total: 0,
                pages: 0
            },
            filters: {
                itemType: '',
                itemCode: '',
                locationId: ''
            }
        });

        // 物料管理状态
        const materials = reactive({
            list: [],
            pagination: {
                page: 1,
                limit: 20,
                total: 0,
                pages: 0
            }
        });

        // 仓库位置状态
        const locations = reactive({
            list: []
        });

        // 操作模态框状态
        const modals = reactive({
            inbound: false,
            outbound: false,
            materialForm: false,
            qrScanner: false,
            batchOperation: false
        });

        // 表单数据
        const forms = reactive({
            inbound: {
                inboundType: 'purchase',
                itemType: 'material',
                itemId: '',
                itemCode: '',
                itemName: '',
                locationId: '',
                quantity: 0,
                unit: 'pcs',
                batchNumber: '',
                supplierId: '',
                orderNumber: '',
                qrCodes: [],
                notes: ''
            },
            outbound: {
                outboundType: 'production',
                outboundReason: '',
                itemType: 'material',
                itemId: '',
                itemCode: '',
                itemName: '',
                locationId: '',
                quantity: 0,
                unit: 'pcs',
                batchId: '',
                customerId: '',
                orderNumber: '',
                destination: '',
                qrCodes: [],
                notes: ''
            },
            material: {
                code: '',
                name: '',
                category: 'raw_material',
                supplierName: '',
                unit: 'pcs',
                specifications: {},
                safetyStock: 0,
                maxStock: 0,
                leadTime: 0,
                isCustomerSupplied: false
            }
        });

        // 二维码扫描状态
        const qrScanner = reactive({
            isActive: false,
            scannedCodes: [],
            currentOperation: '', // 'inbound' | 'outbound'
            scanner: null
        });

        // 计算属性
        const filteredInventory = computed(() => {
            let filtered = inventory.list;
            
            if (searchTerm.value) {
                const term = searchTerm.value.toLowerCase();
                filtered = filtered.filter(item => 
                    item.item_code.toLowerCase().includes(term) ||
                    item.item_name.toLowerCase().includes(term) ||
                    item.location_name?.toLowerCase().includes(term)
                );
            }
            
            return filtered;
        });

        const hasPermission = computed(() => {
            return (permission) => {
                if (!currentUser.value) return false;
                return currentUser.value.role === 'admin' || 
                       (currentUser.value.permissions && currentUser.value.permissions.includes(permission));
            };
        });

        // 生命周期
        onMounted(async () => {
            await initializeApp();
        });

        // 方法
        async function initializeApp() {
            try {
                loading.value = true;
                
                // 获取当前用户信息
                const userJson = sessionStorage.getItem('user');
                if (userJson) {
                    currentUser.value = JSON.parse(userJson);
                }
                
                // 检查权限
                if (!hasPermission.value('warehouse_view')) {
                    alert('您没有访问仓库管理的权限');
                    window.location.href = '/dashboard';
                    return;
                }
                
                // 加载初始数据
                await Promise.all([
                    loadInventory(),
                    loadMaterials(),
                    loadLocations()
                ]);
                
            } catch (error) {
                console.error('初始化应用失败:', error);
                alert('初始化应用失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        }

        async function loadInventory() {
            try {
                const params = {
                    page: inventory.pagination.page,
                    limit: inventory.pagination.limit,
                    ...inventory.filters
                };
                
                const response = await WarehouseAPI.Inventory.getInventory(params);
                
                if (response.success) {
                    inventory.list = response.data.inventory;
                    inventory.pagination = response.data.pagination;
                }
            } catch (error) {
                console.error('加载库存数据失败:', error);
                alert('加载库存数据失败: ' + error.message);
            }
        }

        async function loadMaterials() {
            try {
                const params = {
                    page: materials.pagination.page,
                    limit: materials.pagination.limit,
                    search: searchTerm.value
                };
                
                const response = await WarehouseAPI.Material.getMaterials(params);
                
                if (response.success) {
                    materials.list = response.data.materials;
                    materials.pagination = response.data.pagination;
                }
            } catch (error) {
                console.error('加载物料数据失败:', error);
                alert('加载物料数据失败: ' + error.message);
            }
        }

        async function loadLocations() {
            try {
                const response = await WarehouseAPI.Location.getLocations();
                
                if (response.success) {
                    locations.list = response.data.locations;
                }
            } catch (error) {
                console.error('加载仓库位置失败:', error);
                alert('加载仓库位置失败: ' + error.message);
            }
        }

        function openInboundModal() {
            if (!hasPermission.value('warehouse_inbound')) {
                alert('您没有入库操作权限');
                return;
            }
            
            resetInboundForm();
            modals.inbound = true;
        }

        function openOutboundModal() {
            if (!hasPermission.value('warehouse_outbound')) {
                alert('您没有出库操作权限');
                return;
            }
            
            resetOutboundForm();
            modals.outbound = true;
        }

        function openMaterialForm() {
            if (!hasPermission.value('warehouse_materials_manage')) {
                alert('您没有物料管理权限');
                return;
            }
            
            resetMaterialForm();
            modals.materialForm = true;
        }

        function resetInboundForm() {
            Object.assign(forms.inbound, {
                inboundType: 'purchase',
                itemType: 'material',
                itemId: '',
                itemCode: '',
                itemName: '',
                locationId: '',
                quantity: 0,
                unit: 'pcs',
                batchNumber: '',
                supplierId: '',
                orderNumber: '',
                qrCodes: [],
                notes: ''
            });
        }

        function resetOutboundForm() {
            Object.assign(forms.outbound, {
                outboundType: 'production',
                outboundReason: '',
                itemType: 'material',
                itemId: '',
                itemCode: '',
                itemName: '',
                locationId: '',
                quantity: 0,
                unit: 'pcs',
                batchId: '',
                customerId: '',
                orderNumber: '',
                destination: '',
                qrCodes: [],
                notes: ''
            });
        }

        function resetMaterialForm() {
            Object.assign(forms.material, {
                code: '',
                name: '',
                category: 'raw_material',
                supplierName: '',
                unit: 'pcs',
                specifications: {},
                safetyStock: 0,
                maxStock: 0,
                leadTime: 0,
                isCustomerSupplied: false
            });
        }

        async function submitInbound() {
            try {
                loading.value = true;
                
                // 验证表单
                const validation = validateInboundForm();
                if (!validation.isValid) {
                    alert('表单验证失败:\n' + validation.errors.join('\n'));
                    return;
                }
                
                const response = await WarehouseAPI.Inventory.processInbound(forms.inbound);
                
                if (response.success) {
                    alert('入库操作成功');
                    modals.inbound = false;
                    await loadInventory();
                } else {
                    alert('入库操作失败: ' + response.message);
                }
                
            } catch (error) {
                console.error('入库操作失败:', error);
                alert('入库操作失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        }

        async function submitOutbound() {
            try {
                loading.value = true;
                
                // 验证表单
                const validation = validateOutboundForm();
                if (!validation.isValid) {
                    alert('表单验证失败:\n' + validation.errors.join('\n'));
                    return;
                }
                
                const response = await WarehouseAPI.Inventory.processOutbound(forms.outbound);
                
                if (response.success) {
                    alert('出库操作成功');
                    modals.outbound = false;
                    await loadInventory();
                } else {
                    alert('出库操作失败: ' + response.message);
                }
                
            } catch (error) {
                console.error('出库操作失败:', error);
                alert('出库操作失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        }

        async function submitMaterial() {
            try {
                loading.value = true;
                
                // 验证表单
                const validation = validateMaterialForm();
                if (!validation.isValid) {
                    alert('表单验证失败:\n' + validation.errors.join('\n'));
                    return;
                }
                
                const response = await WarehouseAPI.Material.createMaterial(forms.material);
                
                if (response.success) {
                    alert('物料创建成功');
                    modals.materialForm = false;
                    await loadMaterials();
                } else {
                    alert('物料创建失败: ' + response.message);
                }
                
            } catch (error) {
                console.error('物料创建失败:', error);
                alert('物料创建失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        }

        function validateInboundForm() {
            const errors = [];
            const form = forms.inbound;
            
            if (!form.itemCode) errors.push('物料编码不能为空');
            if (!form.itemName) errors.push('物料名称不能为空');
            if (!form.locationId) errors.push('仓库位置不能为空');
            if (!form.quantity || form.quantity <= 0) errors.push('入库数量必须大于0');
            
            return {
                isValid: errors.length === 0,
                errors
            };
        }

        function validateOutboundForm() {
            const errors = [];
            const form = forms.outbound;
            
            if (!form.itemCode) errors.push('物料编码不能为空');
            if (!form.itemName) errors.push('物料名称不能为空');
            if (!form.locationId) errors.push('仓库位置不能为空');
            if (!form.quantity || form.quantity <= 0) errors.push('出库数量必须大于0');
            
            return {
                isValid: errors.length === 0,
                errors
            };
        }

        function validateMaterialForm() {
            const errors = [];
            const form = forms.material;
            
            if (!form.code) errors.push('物料编码不能为空');
            if (!form.name) errors.push('物料名称不能为空');
            if (!form.category) errors.push('物料分类不能为空');
            
            return {
                isValid: errors.length === 0,
                errors
            };
        }

        // 二维码相关方法
        function openQRScanner(operation) {
            if (!hasPermission.value('warehouse_view')) {
                alert('您没有扫码权限');
                return;
            }

            qrScanner.currentOperation = operation;
            qrScanner.scannedCodes = [];
            modals.qrScanner = true;

            nextTick(() => {
                initQRScanner();
            });
        }

        function initQRScanner() {
            const scannerElement = document.getElementById('qr-scanner');
            if (!scannerElement) return;

            // 使用简化版扫码器
            qrScanner.scanner = new Html5QrcodeScanner('qr-scanner', {
                fps: 10,
                qrbox: { width: 250, height: 250 }
            });

            qrScanner.scanner.render(onScanSuccess, onScanFailure);
            qrScanner.isActive = true;
        }

        function onScanSuccess(qrCode) {
            console.log('扫码成功:', qrCode);

            // 验证二维码格式
            const validation = WarehouseUtils.QRCode.parseQRCode(qrCode);
            if (!validation.isValid) {
                alert('二维码格式错误: ' + validation.message);
                return;
            }

            // 添加到扫码列表
            if (!qrScanner.scannedCodes.includes(qrCode)) {
                qrScanner.scannedCodes.push(qrCode);

                // 根据操作类型更新表单
                if (qrScanner.currentOperation === 'inbound') {
                    forms.inbound.qrCodes = [...qrScanner.scannedCodes];
                    if (!forms.inbound.itemCode && validation.itemCode) {
                        forms.inbound.itemCode = validation.itemCode;
                    }
                } else if (qrScanner.currentOperation === 'outbound') {
                    forms.outbound.qrCodes = [...qrScanner.scannedCodes];
                    if (!forms.outbound.itemCode && validation.itemCode) {
                        forms.outbound.itemCode = validation.itemCode;
                    }
                }
            }
        }

        function onScanFailure(error) {
            console.log('扫码失败:', error);
        }

        function closeQRScanner() {
            if (qrScanner.scanner) {
                qrScanner.scanner.clear();
                qrScanner.scanner = null;
            }
            qrScanner.isActive = false;
            modals.qrScanner = false;
        }

        function removeScannedCode(index) {
            qrScanner.scannedCodes.splice(index, 1);

            // 更新表单
            if (qrScanner.currentOperation === 'inbound') {
                forms.inbound.qrCodes = [...qrScanner.scannedCodes];
            } else if (qrScanner.currentOperation === 'outbound') {
                forms.outbound.qrCodes = [...qrScanner.scannedCodes];
            }
        }

        // 批量操作方法
        function selectAllItems() {
            selectedItems.value = filteredInventory.value.map(item => item.id);
        }

        function clearSelection() {
            selectedItems.value = [];
        }

        function toggleItemSelection(itemId) {
            const index = selectedItems.value.indexOf(itemId);
            if (index > -1) {
                selectedItems.value.splice(index, 1);
            } else {
                selectedItems.value.push(itemId);
            }
        }

        async function batchOperation(operation) {
            if (selectedItems.value.length === 0) {
                alert('请先选择要操作的项目');
                return;
            }

            if (!confirm(`确定要对选中的 ${selectedItems.value.length} 个项目执行 ${operation} 操作吗？`)) {
                return;
            }

            try {
                loading.value = true;

                // 这里可以根据具体操作类型调用相应的API
                // 例如批量出库、批量调整等

                alert(`批量${operation}操作完成`);
                clearSelection();
                await loadInventory();

            } catch (error) {
                console.error(`批量${operation}操作失败:`, error);
                alert(`批量${operation}操作失败: ` + error.message);
            } finally {
                loading.value = false;
            }
        }

        // 搜索和过滤方法
        function applyFilters() {
            inventory.pagination.page = 1;
            loadInventory();
        }

        function clearFilters() {
            inventory.filters.itemType = '';
            inventory.filters.itemCode = '';
            inventory.filters.locationId = '';
            searchTerm.value = '';
            applyFilters();
        }

        function changePage(page) {
            inventory.pagination.page = page;
            loadInventory();
        }

        function changePageSize(size) {
            inventory.pagination.limit = size;
            inventory.pagination.page = 1;
            loadInventory();
        }

        // 导出方法
        async function exportInventory() {
            try {
                loading.value = true;

                // 获取所有库存数据
                const response = await WarehouseAPI.Inventory.getInventory({
                    page: 1,
                    limit: 10000, // 获取所有数据
                    ...inventory.filters
                });

                if (response.success) {
                    const data = response.data.inventory;
                    const csv = convertToCSV(data);
                    downloadCSV(csv, '库存数据.csv');
                }

            } catch (error) {
                console.error('导出库存数据失败:', error);
                alert('导出库存数据失败: ' + error.message);
            } finally {
                loading.value = false;
            }
        }

        function convertToCSV(data) {
            const headers = ['物料编码', '物料名称', '库存数量', '可用数量', '单位', '仓库位置', '批次号', '更新时间'];
            const rows = data.map(item => [
                item.item_code,
                item.item_name,
                item.quantity,
                item.available_quantity,
                item.unit,
                item.location_name || '',
                item.batch_number || '',
                WarehouseUtils.Format.formatDateTime(item.updated_at)
            ]);

            const csvContent = [headers, ...rows]
                .map(row => row.map(field => `"${field}"`).join(','))
                .join('\n');

            return csvContent;
        }

        function downloadCSV(csv, filename) {
            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');

            if (link.download !== undefined) {
                const url = URL.createObjectURL(blob);
                link.setAttribute('href', url);
                link.setAttribute('download', filename);
                link.style.visibility = 'hidden';
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }
        }

        // 返回组件状态和方法
        return {
            // 状态
            loading,
            currentUser,
            activeTab,
            searchTerm,
            selectedItems,
            inventory,
            materials,
            locations,
            modals,
            forms,
            qrScanner,

            // 计算属性
            filteredInventory,
            hasPermission,

            // 方法
            loadInventory,
            loadMaterials,
            loadLocations,
            openInboundModal,
            openOutboundModal,
            openMaterialForm,
            submitInbound,
            submitOutbound,
            submitMaterial,

            // 二维码方法
            openQRScanner,
            closeQRScanner,
            removeScannedCode,

            // 批量操作方法
            selectAllItems,
            clearSelection,
            toggleItemSelection,
            batchOperation,

            // 搜索和过滤方法
            applyFilters,
            clearFilters,
            changePage,
            changePageSize,

            // 导出方法
            exportInventory,

            // 工具方法
            formatDate: WarehouseUtils.Format.formatDateTime,
            formatQuantity: WarehouseUtils.Format.formatQuantity,
            getStockStatus: WarehouseUtils.Status.getStockStatus
        };
    }
};

// 创建并挂载应用
window.warehouseApp = createApp(WarehouseApp);
