/**
 * 仓库管理模块路由入口
 * 定义所有仓库管理相关的API路由
 */

const express = require('express');
const WarehouseController = require('./controller');
const WarehouseModel = require('./model');
const QRCodeController = require('./qrcode/qrcodeController');
const { authenticateJWT, checkPermission } = require('../../middlewares/auth');
const logger = require('../../utils/logger');

function createWarehouseRoutes(db) {
    const router = express.Router();
    
    // 初始化数据库表
    const warehouseModel = new WarehouseModel(db);
    warehouseModel.initializeDefaultData();
    
    // 初始化控制器
    const warehouseController = new WarehouseController(db);
    const qrcodeController = new QRCodeController(db);

    // 库存管理路由
    
    /**
     * 获取库存列表
     * GET /api/warehouse/inventory
     * 权限: warehouse_view
     */
    router.get('/inventory', 
        authenticateJWT, 
        checkPermission('warehouse_view'), 
        (req, res) => warehouseController.getInventory(req, res)
    );

    /**
     * 入库操作
     * POST /api/warehouse/inbound
     * 权限: warehouse_inbound
     */
    router.post('/inbound', 
        authenticateJWT, 
        checkPermission('warehouse_inbound'), 
        (req, res) => warehouseController.processInbound(req, res)
    );

    /**
     * 出库操作
     * POST /api/warehouse/outbound
     * 权限: warehouse_outbound
     */
    router.post('/outbound', 
        authenticateJWT, 
        checkPermission('warehouse_outbound'), 
        (req, res) => warehouseController.processOutbound(req, res)
    );

    /**
     * 获取物料列表
     * GET /api/warehouse/materials
     * 权限: warehouse_view
     */
    router.get('/materials', 
        authenticateJWT, 
        checkPermission('warehouse_view'), 
        (req, res) => warehouseController.getMaterials(req, res)
    );

    /**
     * 创建物料
     * POST /api/warehouse/materials
     * 权限: warehouse_materials_manage
     */
    router.post('/materials', 
        authenticateJWT, 
        checkPermission('warehouse_materials_manage'), 
        (req, res) => warehouseController.createMaterial(req, res)
    );

    /**
     * 获取仓库位置列表
     * GET /api/warehouse/locations
     * 权限: warehouse_view
     */
    router.get('/locations', 
        authenticateJWT, 
        checkPermission('warehouse_view'), 
        (req, res) => warehouseController.getLocations(req, res)
    );

    // 二维码管理路由

    /**
     * 生成二维码
     * POST /api/warehouse/qrcode/generate
     * 权限: warehouse_qrcode_generate
     */
    router.post('/qrcode/generate',
        authenticateJWT,
        checkPermission('warehouse_qrcode_generate'),
        (req, res) => qrcodeController.generateQRCode(req, res)
    );

    /**
     * 批量生成二维码
     * POST /api/warehouse/qrcode/generate-batch
     * 权限: warehouse_qrcode_generate
     */
    router.post('/qrcode/generate-batch',
        authenticateJWT,
        checkPermission('warehouse_qrcode_generate'),
        (req, res) => qrcodeController.generateBatchQRCodes(req, res)
    );

    /**
     * 验证二维码
     * POST /api/warehouse/qrcode/validate
     * 权限: warehouse_view
     */
    router.post('/qrcode/validate',
        authenticateJWT,
        checkPermission('warehouse_view'),
        (req, res) => qrcodeController.validateQRCode(req, res)
    );

    /**
     * 批量验证二维码
     * POST /api/warehouse/qrcode/validate-batch
     * 权限: warehouse_view
     */
    router.post('/qrcode/validate-batch',
        authenticateJWT,
        checkPermission('warehouse_view'),
        (req, res) => qrcodeController.validateBatchQRCodes(req, res)
    );

    /**
     * 解析二维码信息
     * POST /api/warehouse/qrcode/parse
     * 权限: warehouse_view
     */
    router.post('/qrcode/parse',
        authenticateJWT,
        checkPermission('warehouse_view'),
        (req, res) => qrcodeController.parseQRCode(req, res)
    );

    /**
     * 获取二维码历史记录
     * GET /api/warehouse/qrcode/history
     * 权限: warehouse_qrcode_generate
     */
    router.get('/qrcode/history',
        authenticateJWT,
        checkPermission('warehouse_qrcode_generate'),
        (req, res) => qrcodeController.getQRCodeHistory(req, res)
    );

    /**
     * 标记二维码为已使用
     * POST /api/warehouse/qrcode/mark-used
     * 权限: warehouse_inbound, warehouse_outbound
     */
    router.post('/qrcode/mark-used',
        authenticateJWT,
        checkPermission(['warehouse_inbound', 'warehouse_outbound']),
        (req, res) => qrcodeController.markQRCodeUsed(req, res)
    );

    /**
     * 获取二维码统计信息
     * GET /api/warehouse/qrcode/stats
     * 权限: warehouse_reports_view
     */
    router.get('/qrcode/stats',
        authenticateJWT,
        checkPermission('warehouse_reports_view'),
        (req, res) => qrcodeController.getQRCodeStats(req, res)
    );

    // 批次管理路由

    /**
     * 获取批次列表
     * GET /api/warehouse/batches
     * 权限: warehouse_batches_manage
     */
    router.get('/batches', 
        authenticateJWT, 
        checkPermission('warehouse_batches_manage'), 
        async (req, res) => {
            try {
                const { itemType, itemCode, page = 1, limit = 20 } = req.query;
                
                let query = `
                    SELECT * FROM warehouse_batches 
                    WHERE status = 'active'
                `;
                const params = [];
                
                if (itemType) {
                    query += ' AND item_type = ?';
                    params.push(itemType);
                }
                
                if (itemCode) {
                    query += ' AND item_code LIKE ?';
                    params.push(`%${itemCode}%`);
                }
                
                query += ' ORDER BY created_at DESC';
                
                // 分页
                const offset = (page - 1) * limit;
                query += ` LIMIT ? OFFSET ?`;
                params.push(parseInt(limit), offset);
                
                const batches = db.prepare(query).all(...params);
                
                // 获取总数
                let countQuery = `SELECT COUNT(*) as total FROM warehouse_batches WHERE status = 'active'`;
                const countParams = [];
                
                if (itemType) {
                    countQuery += ' AND item_type = ?';
                    countParams.push(itemType);
                }
                
                if (itemCode) {
                    countQuery += ' AND item_code LIKE ?';
                    countParams.push(`%${itemCode}%`);
                }
                
                const { total } = db.prepare(countQuery).get(...countParams);
                
                res.json({
                    success: true,
                    data: {
                        batches,
                        pagination: {
                            page: parseInt(page),
                            limit: parseInt(limit),
                            total,
                            pages: Math.ceil(total / limit)
                        }
                    }
                });
                
            } catch (error) {
                logger.error('获取批次列表失败:', error);
                res.status(500).json({
                    success: false,
                    message: '获取批次列表失败',
                    error: error.message
                });
            }
        }
    );

    /**
     * 获取批次追溯信息
     * GET /api/warehouse/batches/:id/trace
     * 权限: warehouse_batches_manage
     */
    router.get('/batches/:id/trace', 
        authenticateJWT, 
        checkPermission('warehouse_batches_manage'), 
        async (req, res) => {
            try {
                const { id } = req.params;
                
                // 获取批次基本信息
                const batch = db.prepare('SELECT * FROM warehouse_batches WHERE id = ?').get(id);
                if (!batch) {
                    return res.status(404).json({
                        success: false,
                        message: '批次不存在'
                    });
                }
                
                // 获取入库记录
                const inboundRecords = db.prepare(`
                    SELECT * FROM warehouse_inbound 
                    WHERE batch_id = ? 
                    ORDER BY inbound_date DESC
                `).all(id);
                
                // 获取出库记录
                const outboundRecords = db.prepare(`
                    SELECT * FROM warehouse_outbound 
                    WHERE batch_id = ? 
                    ORDER BY outbound_date DESC
                `).all(id);
                
                // 获取操作日志
                const operationLogs = db.prepare(`
                    SELECT * FROM warehouse_operation_logs 
                    WHERE batch_id = ? 
                    ORDER BY operation_time DESC
                `).all(id);
                
                res.json({
                    success: true,
                    data: {
                        batch,
                        inboundRecords,
                        outboundRecords,
                        operationLogs
                    }
                });
                
            } catch (error) {
                logger.error('获取批次追溯信息失败:', error);
                res.status(500).json({
                    success: false,
                    message: '获取批次追溯信息失败',
                    error: error.message
                });
            }
        }
    );

    // 报表路由

    /**
     * 获取库存统计报表
     * GET /api/warehouse/reports/inventory-stats
     * 权限: warehouse_reports_view
     */
    router.get('/reports/inventory-stats', 
        authenticateJWT, 
        checkPermission('warehouse_reports_view'), 
        async (req, res) => {
            try {
                // 总库存统计
                const totalStats = db.prepare(`
                    SELECT 
                        item_type,
                        COUNT(*) as item_count,
                        SUM(quantity) as total_quantity,
                        SUM(available_quantity) as available_quantity
                    FROM warehouse_inventory 
                    GROUP BY item_type
                `).all();
                
                // 按位置统计
                const locationStats = db.prepare(`
                    SELECT 
                        l.name as location_name,
                        l.area,
                        COUNT(i.id) as item_count,
                        SUM(i.quantity) as total_quantity
                    FROM warehouse_locations l
                    LEFT JOIN warehouse_inventory i ON l.id = i.location_id
                    GROUP BY l.id, l.name, l.area
                    ORDER BY l.area, l.name
                `).all();
                
                // 低库存预警
                const lowStockItems = db.prepare(`
                    SELECT 
                        i.*,
                        CASE 
                            WHEN i.item_type = 'material' THEN m.safety_stock
                            WHEN i.item_type = 'product' THEN p.safety_stock
                            ELSE 0
                        END as safety_stock
                    FROM warehouse_inventory i
                    LEFT JOIN warehouse_materials m ON i.item_type = 'material' AND i.item_id = m.id
                    LEFT JOIN warehouse_products p ON i.item_type = 'product' AND i.item_id = p.id
                    WHERE i.available_quantity <= CASE 
                        WHEN i.item_type = 'material' THEN COALESCE(m.safety_stock, 0)
                        WHEN i.item_type = 'product' THEN COALESCE(p.safety_stock, 0)
                        ELSE 0
                    END
                    AND (m.safety_stock > 0 OR p.safety_stock > 0)
                `).all();
                
                res.json({
                    success: true,
                    data: {
                        totalStats,
                        locationStats,
                        lowStockItems
                    }
                });
                
            } catch (error) {
                logger.error('获取库存统计报表失败:', error);
                res.status(500).json({
                    success: false,
                    message: '获取库存统计报表失败',
                    error: error.message
                });
            }
        }
    );

    logger.info('仓库管理路由初始化完成');
    return router;
}

module.exports = createWarehouseRoutes;
