/**
 * 添加库存管理权限脚本
 * 为系统添加库存管理相关权限和权限模板
 */

const path = require('path');
const Database = require('better-sqlite3');
const logger = require('../utils/logger');

async function addWarehousePermissions() {
    const dbPath = path.join(__dirname, '..', 'database', 'application_system.db');
    const db = new Database(dbPath);
    
    try {
        console.log('🚀 开始添加库存管理权限...');
        
        // 库存管理权限列表
        const warehousePermissions = [
            'warehouse_view',
            'warehouse_inbound',
            'warehouse_outbound',
            'warehouse_returns',
            'warehouse_materials_manage',
            'warehouse_products_manage',
            'warehouse_batches_manage',
            'warehouse_qrcode_generate',
            'warehouse_reports_view',
            'warehouse_alerts_manage',
            'warehouse_config_manage',
            'warehouse_adjustments'
        ];
        
        // 1. 为所有admin用户添加库存管理权限
        console.log('📝 为admin用户添加库存管理权限...');
        
        const adminUsers = db.prepare('SELECT id, permissions FROM users WHERE role = ?').all('admin');
        
        for (const user of adminUsers) {
            let currentPermissions = [];
            try {
                currentPermissions = JSON.parse(user.permissions || '[]');
            } catch (e) {
                currentPermissions = [];
            }
            
            // 添加库存管理权限
            const updatedPermissions = [...new Set([...currentPermissions, ...warehousePermissions])];
            
            db.prepare('UPDATE users SET permissions = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?')
              .run(JSON.stringify(updatedPermissions), user.id);
            
            console.log(`✅ 已为admin用户 ${user.id} 添加库存管理权限`);
        }
        
        // 2. 创建库存管理权限模板
        console.log('📋 创建库存管理权限模板...');
        
        const warehouseTemplates = [
            {
                id: 'warehouse_manager',
                name: '仓库管理员权限',
                description: '适用于仓库管理人员的完整权限配置',
                permissions: [
                    'warehouse_view',
                    'warehouse_inbound',
                    'warehouse_outbound',
                    'warehouse_returns',
                    'warehouse_materials_manage',
                    'warehouse_products_manage',
                    'warehouse_batches_manage',
                    'warehouse_qrcode_generate',
                    'warehouse_reports_view',
                    'warehouse_alerts_manage',
                    'warehouse_config_manage',
                    'warehouse_adjustments',
                    'user_settings'
                ]
            },
            {
                id: 'warehouse_operator',
                name: '仓库操作员权限',
                description: '适用于仓库操作人员的基础权限配置',
                permissions: [
                    'warehouse_view',
                    'warehouse_inbound',
                    'warehouse_outbound',
                    'warehouse_returns',
                    'warehouse_qrcode_generate',
                    'user_settings'
                ]
            },
            {
                id: 'warehouse_supervisor',
                name: '仓库主管权限',
                description: '适用于仓库主管的权限配置',
                permissions: [
                    'warehouse_view',
                    'warehouse_inbound',
                    'warehouse_outbound',
                    'warehouse_returns',
                    'warehouse_materials_manage',
                    'warehouse_products_manage',
                    'warehouse_batches_manage',
                    'warehouse_qrcode_generate',
                    'warehouse_reports_view',
                    'warehouse_alerts_manage',
                    'warehouse_adjustments',
                    'user_settings'
                ]
            },
            {
                id: 'warehouse_viewer',
                name: '仓库查看权限',
                description: '适用于只需要查看库存信息的人员',
                permissions: [
                    'warehouse_view',
                    'warehouse_reports_view',
                    'user_settings'
                ]
            }
        ];
        
        for (const template of warehouseTemplates) {
            // 检查模板是否已存在
            const templateExists = db.prepare('SELECT COUNT(*) as count FROM permission_templates WHERE id = ?').get(template.id);
            
            if (templateExists.count === 0) {
                const insertTemplate = db.prepare(`
                    INSERT INTO permission_templates (id, name, description, permissions, is_built_in, created_at, updated_at)
                    VALUES (?, ?, ?, ?, 1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                `);
                
                insertTemplate.run(
                    template.id,
                    template.name,
                    template.description,
                    JSON.stringify(template.permissions)
                );
                
                console.log(`✅ 已创建权限模板: ${template.name}`);
            } else {
                console.log(`ℹ️  权限模板 ${template.name} 已存在`);
            }
        }
        
        // 3. 强制同步WAL文件，确保数据持久化
        db.pragma('wal_checkpoint(TRUNCATE)');
        
        console.log('\n🎉 库存管理权限添加完成！');
        
        logger.info('库存管理权限添加完成', {
            adminUsersCount: adminUsers.length,
            permissions: warehousePermissions,
            templatesCount: warehouseTemplates.length
        });
        
    } catch (error) {
        console.error('❌ 添加库存管理权限失败:', error);
        logger.error('添加库存管理权限失败', {
            error: error.message,
            stack: error.stack
        });
        throw error;
    } finally {
        db.close();
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    addWarehousePermissions()
        .then(() => {
            console.log('✅ 脚本执行完成');
            process.exit(0);
        })
        .catch((error) => {
            console.error('❌ 脚本执行失败:', error);
            process.exit(1);
        });
}

module.exports = { addWarehousePermissions };
