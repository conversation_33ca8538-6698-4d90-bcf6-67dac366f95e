/**
 * 仓库管理API封装
 * 提供统一的前端接口调用
 */

// API基础配置
const API_BASE_URL = '/api/warehouse';

/**
 * 通用API请求函数
 * @param {string} url 请求URL
 * @param {Object} options 请求选项
 * @returns {Promise} 请求结果
 */
async function apiRequest(url, options = {}) {
    try {
        const token = sessionStorage.getItem('token');
        const defaultHeaders = {
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : ''
        };

        const config = {
            headers: { ...defaultHeaders, ...options.headers },
            ...options
        };

        const response = await axios(url, config);
        return response.data;
    } catch (error) {
        console.error('API请求失败:', error);
        
        if (error.response?.status === 401) {
            // 未授权，跳转到登录页
            window.location.href = '/login';
            return;
        }
        
        throw error.response?.data || error;
    }
}

/**
 * 库存管理API
 */
const InventoryAPI = {
    /**
     * 获取库存列表
     * @param {Object} params 查询参数
     * @returns {Promise} 库存列表
     */
    async getInventory(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return await apiRequest(`${API_BASE_URL}/inventory?${queryString}`, {
            method: 'GET'
        });
    },

    /**
     * 入库操作
     * @param {Object} data 入库数据
     * @returns {Promise} 操作结果
     */
    async processInbound(data) {
        return await apiRequest(`${API_BASE_URL}/inbound`, {
            method: 'POST',
            data: data
        });
    },

    /**
     * 出库操作
     * @param {Object} data 出库数据
     * @returns {Promise} 操作结果
     */
    async processOutbound(data) {
        return await apiRequest(`${API_BASE_URL}/outbound`, {
            method: 'POST',
            data: data
        });
    },

    /**
     * 获取库存统计报表
     * @returns {Promise} 统计数据
     */
    async getInventoryStats() {
        return await apiRequest(`${API_BASE_URL}/reports/inventory-stats`, {
            method: 'GET'
        });
    }
};

/**
 * 物料管理API
 */
const MaterialAPI = {
    /**
     * 获取物料列表
     * @param {Object} params 查询参数
     * @returns {Promise} 物料列表
     */
    async getMaterials(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return await apiRequest(`${API_BASE_URL}/materials?${queryString}`, {
            method: 'GET'
        });
    },

    /**
     * 创建物料
     * @param {Object} data 物料数据
     * @returns {Promise} 创建结果
     */
    async createMaterial(data) {
        return await apiRequest(`${API_BASE_URL}/materials`, {
            method: 'POST',
            data: data
        });
    },

    /**
     * 更新物料
     * @param {string} id 物料ID
     * @param {Object} data 物料数据
     * @returns {Promise} 更新结果
     */
    async updateMaterial(id, data) {
        return await apiRequest(`${API_BASE_URL}/materials/${id}`, {
            method: 'PUT',
            data: data
        });
    },

    /**
     * 删除物料
     * @param {string} id 物料ID
     * @returns {Promise} 删除结果
     */
    async deleteMaterial(id) {
        return await apiRequest(`${API_BASE_URL}/materials/${id}`, {
            method: 'DELETE'
        });
    }
};

/**
 * 仓库位置API
 */
const LocationAPI = {
    /**
     * 获取仓库位置列表
     * @param {Object} params 查询参数
     * @returns {Promise} 位置列表
     */
    async getLocations(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return await apiRequest(`${API_BASE_URL}/locations?${queryString}`, {
            method: 'GET'
        });
    }
};

/**
 * 批次管理API
 */
const BatchAPI = {
    /**
     * 获取批次列表
     * @param {Object} params 查询参数
     * @returns {Promise} 批次列表
     */
    async getBatches(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return await apiRequest(`${API_BASE_URL}/batches?${queryString}`, {
            method: 'GET'
        });
    },

    /**
     * 获取批次追溯信息
     * @param {string} id 批次ID
     * @returns {Promise} 追溯信息
     */
    async getBatchTrace(id) {
        return await apiRequest(`${API_BASE_URL}/batches/${id}/trace`, {
            method: 'GET'
        });
    }
};

/**
 * 二维码API
 */
const QRCodeAPI = {
    /**
     * 生成二维码
     * @param {Object} data 二维码数据
     * @returns {Promise} 生成结果
     */
    async generateQRCode(data) {
        return await apiRequest(`${API_BASE_URL}/qrcode/generate`, {
            method: 'POST',
            data: data
        });
    },

    /**
     * 批量生成二维码
     * @param {Object} data 批量数据
     * @returns {Promise} 生成结果
     */
    async generateBatchQRCodes(data) {
        return await apiRequest(`${API_BASE_URL}/qrcode/generate-batch`, {
            method: 'POST',
            data: data
        });
    },

    /**
     * 验证二维码
     * @param {Object} data 验证数据
     * @returns {Promise} 验证结果
     */
    async validateQRCode(data) {
        return await apiRequest(`${API_BASE_URL}/qrcode/validate`, {
            method: 'POST',
            data: data
        });
    },

    /**
     * 批量验证二维码
     * @param {Object} data 批量验证数据
     * @returns {Promise} 验证结果
     */
    async validateBatchQRCodes(data) {
        return await apiRequest(`${API_BASE_URL}/qrcode/validate-batch`, {
            method: 'POST',
            data: data
        });
    },

    /**
     * 解析二维码信息
     * @param {Object} data 解析数据
     * @returns {Promise} 解析结果
     */
    async parseQRCode(data) {
        return await apiRequest(`${API_BASE_URL}/qrcode/parse`, {
            method: 'POST',
            data: data
        });
    },

    /**
     * 获取二维码历史记录
     * @param {Object} params 查询参数
     * @returns {Promise} 历史记录
     */
    async getQRCodeHistory(params = {}) {
        const queryString = new URLSearchParams(params).toString();
        return await apiRequest(`${API_BASE_URL}/qrcode/history?${queryString}`, {
            method: 'GET'
        });
    },

    /**
     * 标记二维码为已使用
     * @param {Object} data 标记数据
     * @returns {Promise} 操作结果
     */
    async markQRCodeUsed(data) {
        return await apiRequest(`${API_BASE_URL}/qrcode/mark-used`, {
            method: 'POST',
            data: data
        });
    },

    /**
     * 获取二维码统计信息
     * @returns {Promise} 统计信息
     */
    async getQRCodeStats() {
        return await apiRequest(`${API_BASE_URL}/qrcode/stats`, {
            method: 'GET'
        });
    }
};

/**
 * 报表API
 */
const ReportAPI = {
    /**
     * 获取库存统计报表
     * @returns {Promise} 统计报表
     */
    async getInventoryStats() {
        return await apiRequest(`${API_BASE_URL}/reports/inventory-stats`, {
            method: 'GET'
        });
    }
};

/**
 * 工具函数
 */
const WarehouseUtils = {
    /**
     * 格式化日期
     * @param {string} dateString 日期字符串
     * @returns {string} 格式化后的日期
     */
    formatDate(dateString) {
        if (!dateString) return '-';
        return new Date(dateString).toLocaleString('zh-CN');
    },

    /**
     * 格式化数量
     * @param {number} quantity 数量
     * @param {string} unit 单位
     * @returns {string} 格式化后的数量
     */
    formatQuantity(quantity, unit = 'pcs') {
        if (quantity === null || quantity === undefined) return '-';
        return `${quantity.toLocaleString()} ${unit}`;
    },

    /**
     * 获取库存状态颜色
     * @param {number} current 当前库存
     * @param {number} safety 安全库存
     * @returns {string} 状态颜色类
     */
    getStockStatusColor(current, safety) {
        if (current <= 0) return 'text-red-600';
        if (current <= safety) return 'text-yellow-600';
        return 'text-green-600';
    },

    /**
     * 获取库存状态文本
     * @param {number} current 当前库存
     * @param {number} safety 安全库存
     * @returns {string} 状态文本
     */
    getStockStatusText(current, safety) {
        if (current <= 0) return '缺货';
        if (current <= safety) return '低库存';
        return '正常';
    },

    /**
     * 验证二维码格式
     * @param {string} qrCode 二维码
     * @returns {Object} 验证结果
     */
    validateQRCodeFormat(qrCode) {
        if (!qrCode || typeof qrCode !== 'string') {
            return { isValid: false, message: '二维码不能为空' };
        }

        // 物料二维码格式: MAT-{供应商代码}-{物料编码}-{批次号}-{序列号}
        if (qrCode.startsWith('MAT-')) {
            if (!/^MAT-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+$/.test(qrCode)) {
                return { isValid: false, message: '物料二维码格式不正确' };
            }
            return { isValid: true, type: 'material' };
        }
        
        // 成品二维码格式: PRD-{产品编码}-{生产日期}-{批次号}-{箱号}
        if (qrCode.startsWith('PRD-')) {
            if (!/^PRD-[A-Z0-9]+-\d{8}-[A-Z0-9]+-[A-Z0-9]+$/.test(qrCode)) {
                return { isValid: false, message: '成品二维码格式不正确' };
            }
            return { isValid: true, type: 'product' };
        }
        
        // 客供物料二维码格式: CUST-{客户代码}-{物料编码}-{批次号}-{序列号}
        if (qrCode.startsWith('CUST-')) {
            if (!/^CUST-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+$/.test(qrCode)) {
                return { isValid: false, message: '客供物料二维码格式不正确' };
            }
            return { isValid: true, type: 'customer_supplied' };
        }
        
        return { isValid: false, message: '不支持的二维码类型' };
    }
};

// 导出API对象
window.WarehouseAPI = {
    Inventory: InventoryAPI,
    Material: MaterialAPI,
    Location: LocationAPI,
    Batch: BatchAPI,
    QRCode: QRCodeAPI,
    Report: ReportAPI,
    Utils: WarehouseUtils
};
