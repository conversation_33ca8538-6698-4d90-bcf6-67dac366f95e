/**
 * 仓库管理控制器
 * 处理HTTP请求和响应
 */

const WarehouseService = require('./service');
const WarehouseValidator = require('./validator');
const logger = require('../../utils/logger');

class WarehouseController {
    constructor(db) {
        this.warehouseService = new WarehouseService(db);
        this.validator = new WarehouseValidator();
    }

    /**
     * 获取库存列表
     */
    async getInventory(req, res) {
        try {
            const { itemType, itemCode, locationId, page = 1, limit = 20 } = req.query;
            
            let query = `
                SELECT 
                    i.*,
                    l.name as location_name,
                    l.area as location_area,
                    b.batch_number,
                    b.production_date,
                    b.expiry_date
                FROM warehouse_inventory i
                LEFT JOIN warehouse_locations l ON i.location_id = l.id
                LEFT JOIN warehouse_batches b ON i.batch_id = b.id
                WHERE 1=1
            `;
            
            const params = [];
            
            if (itemType) {
                query += ' AND i.item_type = ?';
                params.push(itemType);
            }
            
            if (itemCode) {
                query += ' AND i.item_code LIKE ?';
                params.push(`%${itemCode}%`);
            }
            
            if (locationId) {
                query += ' AND i.location_id = ?';
                params.push(locationId);
            }
            
            query += ' ORDER BY i.updated_at DESC';
            
            // 分页
            const offset = (page - 1) * limit;
            query += ` LIMIT ? OFFSET ?`;
            params.push(parseInt(limit), offset);
            
            const inventory = this.warehouseService.db.prepare(query).all(...params);
            
            // 获取总数
            let countQuery = `
                SELECT COUNT(*) as total
                FROM warehouse_inventory i
                WHERE 1=1
            `;
            const countParams = [];
            
            if (itemType) {
                countQuery += ' AND i.item_type = ?';
                countParams.push(itemType);
            }
            
            if (itemCode) {
                countQuery += ' AND i.item_code LIKE ?';
                countParams.push(`%${itemCode}%`);
            }
            
            if (locationId) {
                countQuery += ' AND i.location_id = ?';
                countParams.push(locationId);
            }
            
            const { total } = this.warehouseService.db.prepare(countQuery).get(...countParams);
            
            res.json({
                success: true,
                data: {
                    inventory,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        pages: Math.ceil(total / limit)
                    }
                }
            });
            
        } catch (error) {
            logger.error('获取库存列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取库存列表失败',
                error: error.message
            });
        }
    }

    /**
     * 入库操作
     */
    async processInbound(req, res) {
        try {
            // 验证请求数据
            const validationResult = this.validator.validateInbound(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    success: false,
                    message: '数据验证失败',
                    errors: validationResult.errors
                });
            }

            const inboundData = {
                ...req.body,
                operatorId: req.user.id,
                operatorName: req.user.username
            };

            const result = await this.warehouseService.processInbound(inboundData);

            res.json(result);

        } catch (error) {
            logger.error('入库操作失败:', error);
            res.status(500).json({
                success: false,
                message: '入库操作失败',
                error: error.message
            });
        }
    }

    /**
     * 出库操作
     */
    async processOutbound(req, res) {
        try {
            // 验证请求数据
            const validationResult = this.validator.validateOutbound(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    success: false,
                    message: '数据验证失败',
                    errors: validationResult.errors
                });
            }

            const outboundData = {
                ...req.body,
                operatorId: req.user.id,
                operatorName: req.user.username
            };

            const result = await this.warehouseService.processOutbound(outboundData);

            res.json(result);

        } catch (error) {
            logger.error('出库操作失败:', error);
            res.status(500).json({
                success: false,
                message: '出库操作失败',
                error: error.message
            });
        }
    }

    /**
     * 获取物料列表
     */
    async getMaterials(req, res) {
        try {
            const { page = 1, limit = 20, search } = req.query;
            
            let query = 'SELECT * FROM warehouse_materials WHERE status = "active"';
            const params = [];
            
            if (search) {
                query += ' AND (code LIKE ? OR name LIKE ?)';
                params.push(`%${search}%`, `%${search}%`);
            }
            
            query += ' ORDER BY created_at DESC';
            
            // 分页
            const offset = (page - 1) * limit;
            query += ` LIMIT ? OFFSET ?`;
            params.push(parseInt(limit), offset);
            
            const materials = this.warehouseService.db.prepare(query).all(...params);
            
            // 获取总数
            let countQuery = 'SELECT COUNT(*) as total FROM warehouse_materials WHERE status = "active"';
            const countParams = [];
            
            if (search) {
                countQuery += ' AND (code LIKE ? OR name LIKE ?)';
                countParams.push(`%${search}%`, `%${search}%`);
            }
            
            const { total } = this.warehouseService.db.prepare(countQuery).get(...countParams);
            
            res.json({
                success: true,
                data: {
                    materials: materials.map(material => ({
                        ...material,
                        specifications: JSON.parse(material.specifications || '{}')
                    })),
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        pages: Math.ceil(total / limit)
                    }
                }
            });
            
        } catch (error) {
            logger.error('获取物料列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取物料列表失败',
                error: error.message
            });
        }
    }

    /**
     * 创建物料
     */
    async createMaterial(req, res) {
        try {
            const validationResult = this.validator.validateMaterial(req.body);
            if (!validationResult.isValid) {
                return res.status(400).json({
                    success: false,
                    message: '数据验证失败',
                    errors: validationResult.errors
                });
            }

            const { code, name, category, supplierName, unit, specifications, safetyStock, maxStock, leadTime, isCustomerSupplied } = req.body;
            
            // 检查物料编码是否已存在
            const existingMaterial = this.warehouseService.db.prepare('SELECT id FROM warehouse_materials WHERE code = ?').get(code);
            if (existingMaterial) {
                return res.status(400).json({
                    success: false,
                    message: '物料编码已存在'
                });
            }

            const materialId = require('uuid').v4();
            
            this.warehouseService.db.prepare(`
                INSERT INTO warehouse_materials 
                (id, code, name, category, supplier_name, unit, specifications, 
                 safety_stock, max_stock, lead_time, is_customer_supplied, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `).run(
                materialId, code, name, category, supplierName, unit, 
                JSON.stringify(specifications || {}), safetyStock || 0, maxStock || 0, 
                leadTime || 0, isCustomerSupplied ? 1 : 0
            );

            res.json({
                success: true,
                message: '物料创建成功',
                data: { materialId }
            });

        } catch (error) {
            logger.error('创建物料失败:', error);
            res.status(500).json({
                success: false,
                message: '创建物料失败',
                error: error.message
            });
        }
    }

    /**
     * 获取仓库位置列表
     */
    async getLocations(req, res) {
        try {
            const { type } = req.query;
            
            let query = 'SELECT * FROM warehouse_locations WHERE status = "active"';
            const params = [];
            
            if (type) {
                query += ' AND type = ?';
                params.push(type);
            }
            
            query += ' ORDER BY area, code';
            
            const locations = this.warehouseService.db.prepare(query).all(...params);
            
            res.json({
                success: true,
                data: { locations }
            });
            
        } catch (error) {
            logger.error('获取仓库位置失败:', error);
            res.status(500).json({
                success: false,
                message: '获取仓库位置失败',
                error: error.message
            });
        }
    }

    /**
     * 验证二维码
     */
    async validateQRCode(req, res) {
        try {
            const { qrCode } = req.body;
            
            if (!qrCode) {
                return res.status(400).json({
                    success: false,
                    message: '二维码不能为空'
                });
            }

            const qrInfo = this.warehouseService.statements.getQRCodeByCode.get(qrCode);
            
            if (!qrInfo) {
                // 尝试解析二维码格式
                const parseResult = this.parseQRCodeFormat(qrCode);
                return res.json({
                    success: true,
                    data: {
                        isValid: parseResult.isValid,
                        isNew: true,
                        format: parseResult.format,
                        message: parseResult.isValid ? '二维码格式正确，可以使用' : '二维码格式不正确'
                    }
                });
            }

            res.json({
                success: true,
                data: {
                    isValid: !qrInfo.is_used,
                    isNew: false,
                    qrInfo,
                    message: qrInfo.is_used ? '二维码已被使用' : '二维码有效'
                }
            });

        } catch (error) {
            logger.error('验证二维码失败:', error);
            res.status(500).json({
                success: false,
                message: '验证二维码失败',
                error: error.message
            });
        }
    }

    /**
     * 解析二维码格式
     * @param {string} qrCode 二维码
     * @returns {Object} 解析结果
     */
    parseQRCodeFormat(qrCode) {
        // MAT-{供应商代码}-{物料编码}-{批次号}-{序列号}
        if (/^MAT-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+$/.test(qrCode)) {
            return { isValid: true, format: 'material_standard' };
        }
        
        // PRD-{产品编码}-{生产日期}-{批次号}-{箱号}
        if (/^PRD-[A-Z0-9]+-\d{8}-[A-Z0-9]+-[A-Z0-9]+$/.test(qrCode)) {
            return { isValid: true, format: 'product_standard' };
        }
        
        // CUST-{客户代码}-{物料编码}-{批次号}-{序列号}
        if (/^CUST-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+$/.test(qrCode)) {
            return { isValid: true, format: 'customer_supplied' };
        }
        
        return { isValid: false, format: 'unknown' };
    }
}

module.exports = WarehouseController;
