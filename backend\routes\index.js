/**
 * 路由索引
 * 集中管理所有路由
 */

const express = require('express');
const router = express.Router();
const authRoutes = require('./authRoutes');
const applicationRoutes = require('./applicationRoutes');
const userRoutes = require('./userRoutes');
const logRoutes = require('./logRoutes');
const permissionTemplateRoutes = require('./permissionTemplateRoutes');
const departmentRoutes = require('./departmentRoutes');
const scheduleRoutes = require('./scheduleRoutes');
const schedulingRoutes = require('./scheduling');
const equipmentRoutes = require('./equipmentRoutes');
const qualityRoutes = require('./qualityRoutes');
const fileManagementRoutes = require('./fileManagementRoutes');
const productRoutes = require('./productRoutes');
const capacityRoutes = require('./capacityRoutes');
const tuningRoutes = require('./tuningRoutes');
const maintenanceRoutes = require('./maintenanceRoutes');
const dashboardRoutes = require('./dashboardRoutes');

// 仓库管理模块
const createWarehouseRoutes = require('../modules/warehouse');
const databaseManager = require('../database/database');

// 认证相关路由
router.use('/auth', authRoutes);

// 申请相关路由
router.use('/applications', applicationRoutes);

// 用户相关路由
router.use('/users', userRoutes);

// 权限模板相关路由
router.use('/permission-templates', permissionTemplateRoutes);

// 部门管理相关路由
router.use('/departments', departmentRoutes);

// 生产排程相关路由
router.use('/schedules', scheduleRoutes);

// 智能排程相关路由
router.use('/scheduling', schedulingRoutes);

// 设备管理相关路由
router.use('/equipment', equipmentRoutes);

// 质量管理相关路由
router.use('/quality', qualityRoutes);

// 文件管理相关路由
router.use('/file-management', fileManagementRoutes);

// 产品管理相关路由
router.use('/products', productRoutes);

// 设备产能相关路由
router.use('/', capacityRoutes);

// 算法调优相关路由
router.use('/tuning', tuningRoutes);

// 维修保养记录相关路由
router.use('/maintenance', maintenanceRoutes);

// 日志相关路由
router.use('/logs', logRoutes);

// 主页相关路由
router.use('/dashboard', dashboardRoutes);

// 仓库管理相关路由
const warehouseRoutes = createWarehouseRoutes(databaseManager.getConnection());
router.use('/warehouse', warehouseRoutes);

// 健康检查
router.get('/health', (req, res) => {
    res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

module.exports = router;
