/**
 * 仓库管理服务层
 * 实现核心业务逻辑
 */

const logger = require('../../utils/logger');
const { v4: uuidv4 } = require('uuid');

class WarehouseService {
    constructor(db) {
        this.db = db;
        this.initializeStatements();
    }

    /**
     * 初始化预编译SQL语句
     */
    initializeStatements() {
        // 库存查询语句
        this.statements = {
            // 库存相关
            getInventoryByItem: this.db.prepare(`
                SELECT * FROM warehouse_inventory 
                WHERE item_type = ? AND item_id = ? AND location_id = ?
            `),
            
            updateInventory: this.db.prepare(`
                UPDATE warehouse_inventory 
                SET quantity = ?, available_quantity = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `),
            
            insertInventory: this.db.prepare(`
                INSERT INTO warehouse_inventory 
                (id, item_type, item_id, item_code, item_name, location_id, batch_id, 
                 quantity, available_quantity, unit, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `),
            
            // 批次相关
            getBatchByNumber: this.db.prepare(`
                SELECT * FROM warehouse_batches WHERE batch_number = ?
            `),
            
            insertBatch: this.db.prepare(`
                INSERT INTO warehouse_batches 
                (id, batch_number, item_type, item_id, item_code, item_name, 
                 production_date, expiry_date, supplier_id, total_quantity, 
                 available_quantity, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `),
            
            updateBatchQuantity: this.db.prepare(`
                UPDATE warehouse_batches 
                SET available_quantity = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            `),
            
            // 二维码相关
            getQRCodeByCode: this.db.prepare(`
                SELECT * FROM warehouse_qrcodes WHERE qr_code = ?
            `),
            
            insertQRCode: this.db.prepare(`
                INSERT INTO warehouse_qrcodes 
                (id, qr_code, qr_type, item_type, item_id, item_code, batch_id, 
                 supplier_code, generation_rule, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `),
            
            markQRCodeUsed: this.db.prepare(`
                UPDATE warehouse_qrcodes 
                SET is_used = 1, first_scan_time = ?, scan_count = scan_count + 1, 
                    status = 'used', updated_at = CURRENT_TIMESTAMP
                WHERE qr_code = ?
            `),
            
            // 入库记录
            insertInbound: this.db.prepare(`
                INSERT INTO warehouse_inbound 
                (id, inbound_number, inbound_type, item_type, item_id, item_code, item_name,
                 batch_id, location_id, quantity, unit, supplier_id, order_number, qr_codes,
                 operator_id, operator_name, inbound_date, notes, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `),
            
            // 出库记录
            insertOutbound: this.db.prepare(`
                INSERT INTO warehouse_outbound 
                (id, outbound_number, outbound_type, outbound_reason, item_type, item_id, 
                 item_code, item_name, batch_id, location_id, quantity, unit, customer_id,
                 order_number, destination, qr_codes, operator_id, operator_name, 
                 outbound_date, notes, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `),
            
            // 操作日志
            insertOperationLog: this.db.prepare(`
                INSERT INTO warehouse_operation_logs 
                (id, operation_type, operation_id, item_type, item_id, item_code,
                 quantity_before, quantity_after, quantity_change, location_id, batch_id,
                 operator_id, operator_name, operation_time, notes, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            `)
        };
    }

    /**
     * 入库操作
     * @param {Object} inboundData 入库数据
     * @returns {Object} 操作结果
     */
    async processInbound(inboundData) {
        const transaction = this.db.transaction(() => {
            try {
                const {
                    inboundType, itemType, itemId, itemCode, itemName,
                    locationId, quantity, unit, batchNumber, supplierId,
                    orderNumber, qrCodes, operatorId, operatorName, notes
                } = inboundData;

                // 1. 生成入库单号
                const inboundNumber = this.generateInboundNumber();
                const inboundId = uuidv4();

                // 2. 处理批次信息
                let batchId = null;
                if (batchNumber) {
                    batchId = this.processBatch({
                        batchNumber,
                        itemType,
                        itemId,
                        itemCode,
                        itemName,
                        quantity,
                        supplierId
                    });
                }

                // 3. 更新库存
                this.updateInventoryQuantity({
                    itemType,
                    itemId,
                    itemCode,
                    itemName,
                    locationId,
                    batchId,
                    quantityChange: quantity,
                    unit,
                    operation: 'inbound'
                });

                // 4. 处理二维码
                if (qrCodes && qrCodes.length > 0) {
                    this.processQRCodes(qrCodes, {
                        itemType,
                        itemId,
                        itemCode,
                        batchId,
                        operation: 'inbound'
                    });
                }

                // 5. 记录入库操作
                this.statements.insertInbound.run(
                    inboundId, inboundNumber, inboundType, itemType, itemId, itemCode, itemName,
                    batchId, locationId, quantity, unit, supplierId, orderNumber, 
                    JSON.stringify(qrCodes || []), operatorId, operatorName,
                    new Date().toISOString(), notes, 'completed'
                );

                // 6. 记录操作日志
                this.logOperation({
                    operationType: 'inbound',
                    operationId: inboundId,
                    itemType,
                    itemId,
                    itemCode,
                    quantityChange: quantity,
                    locationId,
                    batchId,
                    operatorId,
                    operatorName,
                    notes: `入库操作: ${inboundNumber}`
                });

                logger.info('入库操作成功', { inboundNumber, itemCode, quantity });
                return {
                    success: true,
                    inboundId,
                    inboundNumber,
                    message: '入库操作成功'
                };

            } catch (error) {
                logger.error('入库操作失败:', error);
                throw error;
            }
        });

        return transaction();
    }

    /**
     * 出库操作
     * @param {Object} outboundData 出库数据
     * @returns {Object} 操作结果
     */
    async processOutbound(outboundData) {
        const transaction = this.db.transaction(() => {
            try {
                const {
                    outboundType, outboundReason, itemType, itemId, itemCode, itemName,
                    locationId, quantity, unit, batchId, customerId, orderNumber,
                    destination, qrCodes, operatorId, operatorName, notes
                } = outboundData;

                // 1. 检查库存是否充足
                const currentInventory = this.getCurrentInventory(itemType, itemId, locationId, batchId);
                if (currentInventory.availableQuantity < quantity) {
                    throw new Error(`库存不足，当前可用数量: ${currentInventory.availableQuantity}，需要数量: ${quantity}`);
                }

                // 2. 生成出库单号
                const outboundNumber = this.generateOutboundNumber();
                const outboundId = uuidv4();

                // 3. 更新库存
                this.updateInventoryQuantity({
                    itemType,
                    itemId,
                    itemCode,
                    itemName,
                    locationId,
                    batchId,
                    quantityChange: -quantity,
                    unit,
                    operation: 'outbound'
                });

                // 4. 更新批次数量
                if (batchId) {
                    this.updateBatchAvailableQuantity(batchId, -quantity);
                }

                // 5. 处理二维码
                if (qrCodes && qrCodes.length > 0) {
                    this.processQRCodes(qrCodes, {
                        itemType,
                        itemId,
                        itemCode,
                        batchId,
                        operation: 'outbound'
                    });
                }

                // 6. 记录出库操作
                this.statements.insertOutbound.run(
                    outboundId, outboundNumber, outboundType, outboundReason, itemType, itemId,
                    itemCode, itemName, batchId, locationId, quantity, unit, customerId,
                    orderNumber, destination, JSON.stringify(qrCodes || []), operatorId, operatorName,
                    new Date().toISOString(), notes, 'completed'
                );

                // 7. 记录操作日志
                this.logOperation({
                    operationType: 'outbound',
                    operationId: outboundId,
                    itemType,
                    itemId,
                    itemCode,
                    quantityChange: -quantity,
                    locationId,
                    batchId,
                    operatorId,
                    operatorName,
                    notes: `出库操作: ${outboundNumber}`
                });

                logger.info('出库操作成功', { outboundNumber, itemCode, quantity });
                return {
                    success: true,
                    outboundId,
                    outboundNumber,
                    message: '出库操作成功'
                };

            } catch (error) {
                logger.error('出库操作失败:', error);
                throw error;
            }
        });

        return transaction();
    }

    /**
     * 处理批次信息
     * @param {Object} batchData 批次数据
     * @returns {string} 批次ID
     */
    processBatch(batchData) {
        const { batchNumber, itemType, itemId, itemCode, itemName, quantity, supplierId } = batchData;

        // 检查批次是否已存在
        let batch = this.statements.getBatchByNumber.get(batchNumber);

        if (!batch) {
            // 创建新批次
            const batchId = uuidv4();
            this.statements.insertBatch.run(
                batchId, batchNumber, itemType, itemId, itemCode, itemName,
                new Date().toISOString(), null, supplierId, quantity, quantity
            );
            return batchId;
        } else {
            // 更新现有批次数量
            const newAvailableQuantity = batch.available_quantity + quantity;
            this.statements.updateBatchQuantity.run(newAvailableQuantity, batch.id);
            return batch.id;
        }
    }

    /**
     * 更新库存数量
     * @param {Object} inventoryData 库存数据
     */
    updateInventoryQuantity(inventoryData) {
        const { itemType, itemId, itemCode, itemName, locationId, batchId, quantityChange, unit } = inventoryData;

        // 查找现有库存记录
        let inventory = this.statements.getInventoryByItem.get(itemType, itemId, locationId);

        if (!inventory) {
            // 创建新库存记录
            const inventoryId = uuidv4();
            const newQuantity = Math.max(0, quantityChange);
            this.statements.insertInventory.run(
                inventoryId, itemType, itemId, itemCode, itemName, locationId, batchId,
                newQuantity, newQuantity, unit
            );
        } else {
            // 更新现有库存
            const newQuantity = Math.max(0, inventory.quantity + quantityChange);
            const newAvailableQuantity = Math.max(0, inventory.available_quantity + quantityChange);
            this.statements.updateInventory.run(newQuantity, newAvailableQuantity, inventory.id);
        }
    }

    /**
     * 处理二维码
     * @param {Array} qrCodes 二维码列表
     * @param {Object} context 上下文信息
     */
    processQRCodes(qrCodes, context) {
        const { itemType, itemId, itemCode, batchId, operation } = context;

        for (const qrCode of qrCodes) {
            if (operation === 'inbound') {
                // 入库时验证二维码
                this.validateQRCodeForInbound(qrCode, { itemType, itemId, itemCode, batchId });
            } else if (operation === 'outbound') {
                // 出库时标记二维码为已使用
                this.statements.markQRCodeUsed.run(new Date().toISOString(), qrCode);
            }
        }
    }

    /**
     * 验证入库二维码
     * @param {string} qrCode 二维码
     * @param {Object} context 上下文信息
     */
    validateQRCodeForInbound(qrCode, context) {
        const existingQR = this.statements.getQRCodeByCode.get(qrCode);

        if (existingQR) {
            if (existingQR.is_used) {
                throw new Error(`二维码 ${qrCode} 已被使用`);
            }
            // 更新扫描次数
            this.statements.markQRCodeUsed.run(new Date().toISOString(), qrCode);
        } else {
            // 创建新的二维码记录
            const qrId = uuidv4();
            const generationRule = this.parseQRCodeRule(qrCode);
            this.statements.insertQRCode.run(
                qrId, qrCode, 'material', context.itemType, context.itemId, context.itemCode,
                context.batchId, null, generationRule
            );
        }
    }

    /**
     * 解析二维码规则
     * @param {string} qrCode 二维码
     * @returns {string} 规则描述
     */
    parseQRCodeRule(qrCode) {
        if (qrCode.startsWith('MAT-')) {
            return 'material_standard';
        } else if (qrCode.startsWith('PRD-')) {
            return 'product_standard';
        } else if (qrCode.startsWith('CUST-')) {
            return 'customer_supplied';
        }
        return 'unknown';
    }

    /**
     * 获取当前库存
     * @param {string} itemType 物料类型
     * @param {string} itemId 物料ID
     * @param {string} locationId 位置ID
     * @param {string} batchId 批次ID
     * @returns {Object} 库存信息
     */
    getCurrentInventory(itemType, itemId, locationId, batchId = null) {
        let query = `
            SELECT * FROM warehouse_inventory
            WHERE item_type = ? AND item_id = ? AND location_id = ?
        `;
        const params = [itemType, itemId, locationId];

        if (batchId) {
            query += ' AND batch_id = ?';
            params.push(batchId);
        }

        const inventory = this.db.prepare(query).get(...params);
        return inventory || { quantity: 0, availableQuantity: 0 };
    }

    /**
     * 更新批次可用数量
     * @param {string} batchId 批次ID
     * @param {number} quantityChange 数量变化
     */
    updateBatchAvailableQuantity(batchId, quantityChange) {
        const batch = this.db.prepare('SELECT * FROM warehouse_batches WHERE id = ?').get(batchId);
        if (batch) {
            const newAvailableQuantity = Math.max(0, batch.available_quantity + quantityChange);
            this.statements.updateBatchQuantity.run(newAvailableQuantity, batchId);
        }
    }

    /**
     * 记录操作日志
     * @param {Object} logData 日志数据
     */
    logOperation(logData) {
        const {
            operationType, operationId, itemType, itemId, itemCode,
            quantityChange, locationId, batchId, operatorId, operatorName, notes
        } = logData;

        const logId = uuidv4();
        this.statements.insertOperationLog.run(
            logId, operationType, operationId, itemType, itemId, itemCode,
            null, null, quantityChange, locationId, batchId,
            operatorId, operatorName, new Date().toISOString(), notes
        );
    }

    /**
     * 生成入库单号
     * @returns {string} 入库单号
     */
    generateInboundNumber() {
        const date = new Date();
        const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
        const timeStr = date.getTime().toString().slice(-6);
        return `IN${dateStr}${timeStr}`;
    }

    /**
     * 生成出库单号
     * @returns {string} 出库单号
     */
    generateOutboundNumber() {
        const date = new Date();
        const dateStr = date.toISOString().slice(0, 10).replace(/-/g, '');
        const timeStr = date.getTime().toString().slice(-6);
        return `OUT${dateStr}${timeStr}`;
    }
}

module.exports = WarehouseService;
