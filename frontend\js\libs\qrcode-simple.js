/**
 * 简化版二维码生成库
 * 用于生成二维码的基础功能
 */

(function(global) {
    'use strict';

    // 简单的二维码生成器
    function QRCodeGenerator() {
        this.version = '1.0.0';
    }

    QRCodeGenerator.prototype = {
        /**
         * 生成二维码数据URL
         * @param {string} text 要编码的文本
         * @param {Object} options 选项
         * @returns {Promise<string>} 数据URL
         */
        toDataURL: function(text, options) {
            return new Promise((resolve, reject) => {
                try {
                    options = options || {};
                    const size = options.width || 200;
                    const margin = options.margin || 4;
                    
                    // 创建canvas
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = size;
                    canvas.height = size;
                    
                    // 填充白色背景
                    ctx.fillStyle = '#ffffff';
                    ctx.fillRect(0, 0, size, size);
                    
                    // 生成简单的二维码模式（模拟）
                    this.drawSimpleQRPattern(ctx, text, size, margin);
                    
                    resolve(canvas.toDataURL());
                } catch (error) {
                    reject(error);
                }
            });
        },

        /**
         * 生成二维码到Canvas
         * @param {HTMLCanvasElement} canvas 画布
         * @param {string} text 要编码的文本
         * @param {Object} options 选项
         */
        toCanvas: function(canvas, text, options) {
            return new Promise((resolve, reject) => {
                try {
                    options = options || {};
                    const size = Math.min(canvas.width, canvas.height);
                    const margin = options.margin || 4;
                    
                    const ctx = canvas.getContext('2d');
                    
                    // 填充白色背景
                    ctx.fillStyle = '#ffffff';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // 生成简单的二维码模式
                    this.drawSimpleQRPattern(ctx, text, size, margin);
                    
                    resolve(canvas);
                } catch (error) {
                    reject(error);
                }
            });
        },

        /**
         * 绘制简单的二维码模式
         * @param {CanvasRenderingContext2D} ctx 画布上下文
         * @param {string} text 文本
         * @param {number} size 尺寸
         * @param {number} margin 边距
         */
        drawSimpleQRPattern: function(ctx, text, size, margin) {
            const moduleSize = Math.floor((size - margin * 2) / 25); // 25x25 模块
            const startX = margin;
            const startY = margin;
            
            ctx.fillStyle = '#000000';
            
            // 绘制定位标记（左上角）
            this.drawFinderPattern(ctx, startX, startY, moduleSize);
            
            // 绘制定位标记（右上角）
            this.drawFinderPattern(ctx, startX + 18 * moduleSize, startY, moduleSize);
            
            // 绘制定位标记（左下角）
            this.drawFinderPattern(ctx, startX, startY + 18 * moduleSize, moduleSize);
            
            // 根据文本内容生成伪随机模式
            const hash = this.simpleHash(text);
            for (let i = 0; i < 25; i++) {
                for (let j = 0; j < 25; j++) {
                    // 跳过定位标记区域
                    if (this.isFinderPatternArea(i, j)) continue;
                    
                    // 根据哈希值决定是否填充
                    if ((hash + i * 25 + j) % 3 === 0) {
                        ctx.fillRect(
                            startX + i * moduleSize,
                            startY + j * moduleSize,
                            moduleSize,
                            moduleSize
                        );
                    }
                }
            }
            
            // 在中心添加文本标识
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(startX + 8 * moduleSize, startY + 8 * moduleSize, 9 * moduleSize, 9 * moduleSize);
            ctx.fillStyle = '#000000';
            ctx.font = `${moduleSize}px Arial`;
            ctx.textAlign = 'center';
            ctx.fillText(
                text.substring(0, 8),
                startX + 12.5 * moduleSize,
                startY + 13 * moduleSize
            );
        },

        /**
         * 绘制定位标记
         */
        drawFinderPattern: function(ctx, x, y, moduleSize) {
            // 外框 7x7
            ctx.fillRect(x, y, 7 * moduleSize, 7 * moduleSize);
            
            // 内部白色 5x5
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(x + moduleSize, y + moduleSize, 5 * moduleSize, 5 * moduleSize);
            
            // 中心黑色 3x3
            ctx.fillStyle = '#000000';
            ctx.fillRect(x + 2 * moduleSize, y + 2 * moduleSize, 3 * moduleSize, 3 * moduleSize);
        },

        /**
         * 检查是否是定位标记区域
         */
        isFinderPatternArea: function(i, j) {
            // 左上角
            if (i < 9 && j < 9) return true;
            // 右上角
            if (i > 15 && j < 9) return true;
            // 左下角
            if (i < 9 && j > 15) return true;
            return false;
        },

        /**
         * 简单哈希函数
         */
        simpleHash: function(str) {
            let hash = 0;
            for (let i = 0; i < str.length; i++) {
                const char = str.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // 转换为32位整数
            }
            return Math.abs(hash);
        }
    };

    // 创建全局实例
    const QRCode = new QRCodeGenerator();

    // 导出到全局
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = QRCode;
    } else {
        global.QRCode = QRCode;
    }

})(typeof window !== 'undefined' ? window : this);

/**
 * 简化版扫码功能
 * 提供基础的扫码界面
 */
(function(global) {
    'use strict';

    function Html5QrcodeScanner(elementId, config) {
        this.elementId = elementId;
        this.config = config || {};
        this.isScanning = false;
        this.onScanSuccess = null;
        this.onScanFailure = null;
    }

    Html5QrcodeScanner.prototype = {
        /**
         * 渲染扫码界面
         */
        render: function(onScanSuccess, onScanFailure) {
            this.onScanSuccess = onScanSuccess;
            this.onScanFailure = onScanFailure;
            
            const element = document.getElementById(this.elementId);
            if (!element) {
                console.error('扫码容器元素不存在:', this.elementId);
                return;
            }
            
            element.innerHTML = `
                <div class="qr-scanner-container">
                    <div class="qr-scanner-header">
                        <h3>二维码扫描</h3>
                    </div>
                    <div class="qr-scanner-input">
                        <input type="text" id="qr-input" placeholder="请输入或扫描二维码" class="w-full p-2 border rounded">
                        <button id="scan-btn" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                            确认扫码
                        </button>
                    </div>
                    <div class="qr-scanner-tips mt-4 text-sm text-gray-600">
                        <p>提示：请手动输入二维码内容，或使用专业扫码设备</p>
                    </div>
                </div>
            `;
            
            // 绑定事件
            const input = document.getElementById('qr-input');
            const button = document.getElementById('scan-btn');
            
            const handleScan = () => {
                const qrCode = input.value.trim();
                if (qrCode) {
                    if (this.onScanSuccess) {
                        this.onScanSuccess(qrCode);
                    }
                    input.value = '';
                } else {
                    if (this.onScanFailure) {
                        this.onScanFailure('请输入二维码内容');
                    }
                }
            };
            
            button.addEventListener('click', handleScan);
            input.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    handleScan();
                }
            });
            
            // 自动聚焦
            input.focus();
        },

        /**
         * 清理扫码界面
         */
        clear: function() {
            const element = document.getElementById(this.elementId);
            if (element) {
                element.innerHTML = '';
            }
        }
    };

    // 导出到全局
    if (typeof module !== 'undefined' && module.exports) {
        module.exports = { Html5QrcodeScanner };
    } else {
        global.Html5QrcodeScanner = Html5QrcodeScanner;
    }

})(typeof window !== 'undefined' ? window : this);
