/**
 * 仓库管理模块数据模型
 * 定义所有仓库相关的数据表结构和操作
 */

const logger = require('../../utils/logger');

class WarehouseModel {
    constructor(db) {
        this.db = db;
        this.createTables();
    }

    /**
     * 创建仓库管理相关数据表
     */
    createTables() {
        try {
            // 1. 仓库位置表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_locations (
                    id TEXT PRIMARY KEY,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    type TEXT NOT NULL, -- 'raw_material', 'finished_product', 'return'
                    area TEXT NOT NULL,
                    capacity INTEGER DEFAULT 0,
                    current_usage INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'active', -- 'active', 'inactive', 'maintenance'
                    description TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // 2. 物料基础信息表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_materials (
                    id TEXT PRIMARY KEY,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    category TEXT NOT NULL, -- 'raw_material', 'component', 'packaging'
                    supplier_id TEXT,
                    supplier_name TEXT,
                    unit TEXT DEFAULT 'pcs',
                    specifications TEXT DEFAULT '{}',
                    safety_stock INTEGER DEFAULT 0,
                    max_stock INTEGER DEFAULT 0,
                    lead_time INTEGER DEFAULT 0, -- 采购周期（天）
                    is_customer_supplied INTEGER DEFAULT 0, -- 是否客供物料
                    status TEXT DEFAULT 'active',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // 3. 成品基础信息表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_products (
                    id TEXT PRIMARY KEY,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    category TEXT,
                    unit TEXT DEFAULT 'pcs',
                    package_specs TEXT DEFAULT '{}', -- 包装规格：每箱盒数、每盒PCS等
                    specifications TEXT DEFAULT '{}',
                    safety_stock INTEGER DEFAULT 0,
                    max_stock INTEGER DEFAULT 0,
                    shelf_life INTEGER DEFAULT 0, -- 保质期（天）
                    status TEXT DEFAULT 'active',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // 4. 供应商信息表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_suppliers (
                    id TEXT PRIMARY KEY,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    contact_person TEXT,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // 5. 客户信息表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_customers (
                    id TEXT PRIMARY KEY,
                    code TEXT UNIQUE NOT NULL,
                    name TEXT NOT NULL,
                    contact_person TEXT,
                    phone TEXT,
                    email TEXT,
                    address TEXT,
                    status TEXT DEFAULT 'active',
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // 6. 批次管理表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_batches (
                    id TEXT PRIMARY KEY,
                    batch_number TEXT UNIQUE NOT NULL,
                    item_type TEXT NOT NULL, -- 'material', 'product'
                    item_id TEXT NOT NULL,
                    item_code TEXT NOT NULL,
                    item_name TEXT NOT NULL,
                    production_date TEXT,
                    expiry_date TEXT,
                    supplier_id TEXT,
                    supplier_batch TEXT, -- 供应商批次号
                    quality_status TEXT DEFAULT 'qualified', -- 'qualified', 'unqualified', 'pending'
                    total_quantity INTEGER NOT NULL,
                    available_quantity INTEGER NOT NULL,
                    reserved_quantity INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'active', -- 'active', 'consumed', 'expired'
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // 7. 库存数据表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_inventory (
                    id TEXT PRIMARY KEY,
                    item_type TEXT NOT NULL, -- 'material', 'product'
                    item_id TEXT NOT NULL,
                    item_code TEXT NOT NULL,
                    item_name TEXT NOT NULL,
                    location_id TEXT NOT NULL,
                    batch_id TEXT,
                    quantity INTEGER NOT NULL DEFAULT 0,
                    reserved_quantity INTEGER DEFAULT 0, -- 预留数量
                    available_quantity INTEGER NOT NULL DEFAULT 0,
                    unit TEXT DEFAULT 'pcs',
                    last_inbound_date TEXT,
                    last_outbound_date TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (location_id) REFERENCES warehouse_locations (id),
                    FOREIGN KEY (batch_id) REFERENCES warehouse_batches (id)
                )
            `);

            // 8. 二维码管理表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_qrcodes (
                    id TEXT PRIMARY KEY,
                    qr_code TEXT UNIQUE NOT NULL,
                    qr_type TEXT NOT NULL, -- 'material', 'product', 'batch'
                    item_type TEXT NOT NULL,
                    item_id TEXT NOT NULL,
                    item_code TEXT NOT NULL,
                    batch_id TEXT,
                    supplier_code TEXT,
                    generation_rule TEXT NOT NULL, -- 生成规则
                    is_used INTEGER DEFAULT 0,
                    first_scan_time TEXT,
                    scan_count INTEGER DEFAULT 0,
                    status TEXT DEFAULT 'active', -- 'active', 'used', 'invalid'
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (batch_id) REFERENCES warehouse_batches (id)
                )
            `);

            // 9. 入库记录表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_inbound (
                    id TEXT PRIMARY KEY,
                    inbound_number TEXT UNIQUE NOT NULL,
                    inbound_type TEXT NOT NULL, -- 'purchase', 'production', 'return', 'transfer'
                    item_type TEXT NOT NULL,
                    item_id TEXT NOT NULL,
                    item_code TEXT NOT NULL,
                    item_name TEXT NOT NULL,
                    batch_id TEXT,
                    location_id TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    unit TEXT DEFAULT 'pcs',
                    supplier_id TEXT,
                    order_number TEXT, -- 订单号
                    qr_codes TEXT DEFAULT '[]', -- 扫描的二维码列表
                    operator_id TEXT NOT NULL,
                    operator_name TEXT NOT NULL,
                    inbound_date TEXT NOT NULL,
                    notes TEXT,
                    status TEXT DEFAULT 'completed', -- 'pending', 'processing', 'completed', 'cancelled'
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (location_id) REFERENCES warehouse_locations (id),
                    FOREIGN KEY (batch_id) REFERENCES warehouse_batches (id),
                    FOREIGN KEY (operator_id) REFERENCES users (id)
                )
            `);

            // 10. 出库记录表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_outbound (
                    id TEXT PRIMARY KEY,
                    outbound_number TEXT UNIQUE NOT NULL,
                    outbound_type TEXT NOT NULL, -- 'production', 'sale', 'return', 'transfer'
                    outbound_reason TEXT, -- 出库原因
                    item_type TEXT NOT NULL,
                    item_id TEXT NOT NULL,
                    item_code TEXT NOT NULL,
                    item_name TEXT NOT NULL,
                    batch_id TEXT,
                    location_id TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    unit TEXT DEFAULT 'pcs',
                    customer_id TEXT,
                    order_number TEXT, -- 订单号
                    destination TEXT, -- 目的地
                    qr_codes TEXT DEFAULT '[]', -- 扫描的二维码列表
                    operator_id TEXT NOT NULL,
                    operator_name TEXT NOT NULL,
                    outbound_date TEXT NOT NULL,
                    notes TEXT,
                    status TEXT DEFAULT 'completed', -- 'pending', 'processing', 'completed', 'cancelled'
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (location_id) REFERENCES warehouse_locations (id),
                    FOREIGN KEY (batch_id) REFERENCES warehouse_batches (id),
                    FOREIGN KEY (operator_id) REFERENCES users (id)
                )
            `);

            // 11. 退料记录表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_returns (
                    id TEXT PRIMARY KEY,
                    return_number TEXT UNIQUE NOT NULL,
                    return_type TEXT NOT NULL, -- 'workshop_return', 'supplier_return', 'customer_return'
                    return_reason TEXT NOT NULL, -- 'excess', 'quality_issue', 'specification_mismatch', 'quantity_error', 'other'
                    item_type TEXT NOT NULL,
                    item_id TEXT NOT NULL,
                    item_code TEXT NOT NULL,
                    item_name TEXT NOT NULL,
                    batch_id TEXT,
                    location_id TEXT NOT NULL,
                    quantity INTEGER NOT NULL,
                    unit TEXT DEFAULT 'pcs',
                    original_transaction_id TEXT, -- 原始交易ID（入库或出库）
                    supplier_id TEXT,
                    customer_id TEXT,
                    qr_codes TEXT DEFAULT '[]',
                    operator_id TEXT NOT NULL,
                    operator_name TEXT NOT NULL,
                    return_date TEXT NOT NULL,
                    quality_check_result TEXT, -- 质量检查结果
                    notes TEXT,
                    status TEXT DEFAULT 'completed', -- 'pending', 'processing', 'completed', 'cancelled'
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (location_id) REFERENCES warehouse_locations (id),
                    FOREIGN KEY (batch_id) REFERENCES warehouse_batches (id),
                    FOREIGN KEY (operator_id) REFERENCES users (id)
                )
            `);

            // 12. 库存预警表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_alerts (
                    id TEXT PRIMARY KEY,
                    alert_type TEXT NOT NULL, -- 'low_stock', 'overstock', 'expiry_warning', 'quality_issue'
                    item_type TEXT NOT NULL,
                    item_id TEXT NOT NULL,
                    item_code TEXT NOT NULL,
                    item_name TEXT NOT NULL,
                    current_quantity INTEGER,
                    threshold_quantity INTEGER,
                    location_id TEXT,
                    batch_id TEXT,
                    alert_level TEXT DEFAULT 'medium', -- 'low', 'medium', 'high', 'critical'
                    message TEXT NOT NULL,
                    is_read INTEGER DEFAULT 0,
                    is_resolved INTEGER DEFAULT 0,
                    resolved_by TEXT,
                    resolved_at TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (location_id) REFERENCES warehouse_locations (id),
                    FOREIGN KEY (batch_id) REFERENCES warehouse_batches (id)
                )
            `);

            // 13. 操作日志表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_operation_logs (
                    id TEXT PRIMARY KEY,
                    operation_type TEXT NOT NULL, -- 'inbound', 'outbound', 'return', 'transfer', 'adjustment'
                    operation_id TEXT NOT NULL, -- 关联的操作记录ID
                    item_type TEXT NOT NULL,
                    item_id TEXT NOT NULL,
                    item_code TEXT NOT NULL,
                    quantity_before INTEGER,
                    quantity_after INTEGER,
                    quantity_change INTEGER NOT NULL,
                    location_id TEXT,
                    batch_id TEXT,
                    operator_id TEXT NOT NULL,
                    operator_name TEXT NOT NULL,
                    operation_time TEXT NOT NULL,
                    ip_address TEXT,
                    user_agent TEXT,
                    notes TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // 14. 系统配置表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_system_config (
                    id TEXT PRIMARY KEY,
                    config_key TEXT UNIQUE NOT NULL,
                    config_value TEXT NOT NULL,
                    config_type TEXT DEFAULT 'string', -- 'string', 'number', 'boolean', 'json'
                    description TEXT,
                    is_system INTEGER DEFAULT 0, -- 是否系统配置
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            `);

            // 15. 库存调整记录表
            this.db.exec(`
                CREATE TABLE IF NOT EXISTS warehouse_adjustments (
                    id TEXT PRIMARY KEY,
                    adjustment_number TEXT UNIQUE NOT NULL,
                    adjustment_type TEXT NOT NULL, -- 'increase', 'decrease', 'correction'
                    adjustment_reason TEXT NOT NULL, -- 'inventory_check', 'damage', 'loss', 'found', 'system_error'
                    item_type TEXT NOT NULL,
                    item_id TEXT NOT NULL,
                    item_code TEXT NOT NULL,
                    item_name TEXT NOT NULL,
                    batch_id TEXT,
                    location_id TEXT NOT NULL,
                    quantity_before INTEGER NOT NULL,
                    quantity_after INTEGER NOT NULL,
                    adjustment_quantity INTEGER NOT NULL,
                    unit TEXT DEFAULT 'pcs',
                    operator_id TEXT NOT NULL,
                    operator_name TEXT NOT NULL,
                    approver_id TEXT,
                    approver_name TEXT,
                    adjustment_date TEXT NOT NULL,
                    approval_date TEXT,
                    notes TEXT,
                    status TEXT DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (location_id) REFERENCES warehouse_locations (id),
                    FOREIGN KEY (batch_id) REFERENCES warehouse_batches (id),
                    FOREIGN KEY (operator_id) REFERENCES users (id),
                    FOREIGN KEY (approver_id) REFERENCES users (id)
                )
            `);

            // 创建索引以提高查询性能
            this.createIndexes();

            logger.info('仓库管理数据表创建成功');
        } catch (error) {
            logger.error('创建仓库管理数据表失败:', error);
            throw error;
        }
    }

    /**
     * 创建数据库索引
     */
    createIndexes() {
        try {
            // 库存表索引
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_inventory_item ON warehouse_inventory(item_type, item_id)');
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_inventory_location ON warehouse_inventory(location_id)');
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_inventory_batch ON warehouse_inventory(batch_id)');

            // 交易记录索引
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_inbound_date ON warehouse_inbound(inbound_date)');
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_outbound_date ON warehouse_outbound(outbound_date)');
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_returns_date ON warehouse_returns(return_date)');

            // 二维码索引
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_qrcodes_code ON warehouse_qrcodes(qr_code)');
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_qrcodes_item ON warehouse_qrcodes(item_type, item_id)');

            // 批次索引
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_batches_number ON warehouse_batches(batch_number)');
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_batches_item ON warehouse_batches(item_type, item_id)');

            // 预警索引
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_alerts_type ON warehouse_alerts(alert_type)');
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_alerts_resolved ON warehouse_alerts(is_resolved)');

            // 操作日志索引
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_logs_operation ON warehouse_operation_logs(operation_type, operation_time)');
            this.db.exec('CREATE INDEX IF NOT EXISTS idx_warehouse_logs_operator ON warehouse_operation_logs(operator_id)');

            logger.info('仓库管理数据库索引创建成功');
        } catch (error) {
            logger.error('创建仓库管理数据库索引失败:', error);
            throw error;
        }
    }

    /**
     * 初始化默认数据
     */
    initializeDefaultData() {
        try {
            // 插入默认仓库位置
            const defaultLocations = [
                { id: 'LOC001', code: 'RM-A01', name: '原料区A01', type: 'raw_material', area: 'A区' },
                { id: 'LOC002', code: 'RM-A02', name: '原料区A02', type: 'raw_material', area: 'A区' },
                { id: 'LOC003', code: 'FP-B01', name: '成品区B01', type: 'finished_product', area: 'B区' },
                { id: 'LOC004', code: 'FP-B02', name: '成品区B02', type: 'finished_product', area: 'B区' },
                { id: 'LOC005', code: 'RT-C01', name: '退货区C01', type: 'return', area: 'C区' }
            ];

            const insertLocation = this.db.prepare(`
                INSERT OR IGNORE INTO warehouse_locations
                (id, code, name, type, area, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `);

            for (const location of defaultLocations) {
                insertLocation.run(location.id, location.code, location.name, location.type, location.area);
            }

            // 插入默认系统配置
            const defaultConfigs = [
                { key: 'qr_code_prefix_material', value: 'MAT', description: '物料二维码前缀' },
                { key: 'qr_code_prefix_product', value: 'PRD', description: '成品二维码前缀' },
                { key: 'qr_code_prefix_customer', value: 'CUST', description: '客供物料二维码前缀' },
                { key: 'low_stock_alert_enabled', value: 'true', description: '启用低库存预警' },
                { key: 'expiry_alert_days', value: '30', description: '到期预警天数' },
                { key: 'auto_generate_batch', value: 'true', description: '自动生成批次号' }
            ];

            const insertConfig = this.db.prepare(`
                INSERT OR IGNORE INTO warehouse_system_config
                (id, config_key, config_value, description, created_at, updated_at)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `);

            for (const config of defaultConfigs) {
                const id = `CFG_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
                insertConfig.run(id, config.key, config.value, config.description);
            }

            logger.info('仓库管理默认数据初始化成功');
        } catch (error) {
            logger.error('初始化仓库管理默认数据失败:', error);
            throw error;
        }
    }
}

module.exports = WarehouseModel;
