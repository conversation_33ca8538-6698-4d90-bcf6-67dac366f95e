# 仓库管理系统实现方案

## 📋 系统概述

基于当前Makrite管理系统架构，仓库管理系统作为核心业务模块，负责原物料和成品的全生命周期管理。系统采用Vue.js 3 + Node.js + SQLite技术栈，与现有权限管理、用户管理等模块深度集成。

### 核心特性
- 🏭 **物料全流程管理** - 从到厂到发料的完整追溯
- 📦 **成品智能管理** - 批次管理和库存优化
- 🔍 **条码扫描系统** - 实时验证和异常处理
- 📊 **库存预警机制** - 智能预警和补货提醒
- 🔒 **权限精细控制** - 基于RBAC的操作权限管理

## 🏗️ 技术架构设计

### 系统架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端层 (Vue3)  │    │   API层 (Node)   │    │  数据层 (SQLite) │
├─────────────────┤    ├─────────────────┤    ├─────────────────┤
│ 仓库管理页面     │◄──►│ 仓库控制器       │◄──►│ 物料表           │
│ 条码扫描组件     │    │ 库存服务         │    │ 成品表           │
│ 库存监控面板     │    │ 条码服务         │    │ 库存表           │
│ 报表统计组件     │    │ 追溯服务         │    │ 事务记录表       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 技术栈配置
- **前端框架**: Vue.js 3 (Composition API)
- **样式框架**: Tailwind CSS
- **HTTP客户端**: Axios
- **后端框架**: Node.js + Express
- **数据库**: SQLite
- **权限管理**: 基于现有RBAC系统

## 📊 数据库设计

### 核心数据表结构

#### 1. 物料表 (materials)
```sql
CREATE TABLE materials (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    material_code VARCHAR(50) UNIQUE NOT NULL,     -- 物料编码
    material_name VARCHAR(200) NOT NULL,           -- 物料名称
    material_type VARCHAR(50) NOT NULL,            -- 物料类型(常规/客供)
    supplier_code VARCHAR(50),                     -- 供应商代码
    supplier_name VARCHAR(200),                    -- 供应商名称
    unit VARCHAR(20) NOT NULL,                     -- 单位
    safety_stock INTEGER DEFAULT 0,               -- 安全库存
    current_stock INTEGER DEFAULT 0,              -- 当前库存
    status VARCHAR(20) DEFAULT 'active',          -- 状态
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 2. 成品表 (finished_products)
```sql
CREATE TABLE finished_products (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    product_code VARCHAR(50) UNIQUE NOT NULL,      -- 成品编码
    product_name VARCHAR(200) NOT NULL,            -- 成品名称
    batch_number VARCHAR(50),                      -- 批次号
    boxes_per_unit INTEGER DEFAULT 1,             -- 每箱盒数
    pieces_per_box INTEGER DEFAULT 1,             -- 每盒件数
    total_pieces INTEGER,                          -- 总件数
    current_stock INTEGER DEFAULT 0,              -- 当前库存
    production_date DATE,                          -- 生产日期
    expiry_date DATE,                             -- 有效期
    status VARCHAR(20) DEFAULT 'active',          -- 状态
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 3. 库存事务表 (inventory_transactions)
```sql
CREATE TABLE inventory_transactions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    transaction_id VARCHAR(50) UNIQUE NOT NULL,    -- 事务编号
    item_type VARCHAR(20) NOT NULL,               -- 物品类型(material/product)
    item_id INTEGER NOT NULL,                     -- 物品ID
    transaction_type VARCHAR(20) NOT NULL,        -- 事务类型(inbound/outbound/return)
    quantity INTEGER NOT NULL,                    -- 数量
    unit VARCHAR(20) NOT NULL,                    -- 单位
    barcode VARCHAR(100),                         -- 条码
    reason VARCHAR(100),                          -- 原因
    operator_id INTEGER NOT NULL,                -- 操作员ID
    order_number VARCHAR(50),                     -- 订单号
    supplier_info TEXT,                           -- 供应商信息
    customer_info TEXT,                           -- 客户信息
    notes TEXT,                                   -- 备注
    transaction_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

#### 4. 条码管理表 (barcodes)
```sql
CREATE TABLE barcodes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    barcode VARCHAR(100) UNIQUE NOT NULL,         -- 条码
    item_type VARCHAR(20) NOT NULL,               -- 物品类型
    item_id INTEGER NOT NULL,                     -- 物品ID
    batch_info VARCHAR(100),                      -- 批次信息
    quantity INTEGER,                             -- 数量
    status VARCHAR(20) DEFAULT 'active',          -- 状态
    generated_by INTEGER NOT NULL,               -- 生成人
    used_by INTEGER,                             -- 使用人
    used_at DATETIME,                            -- 使用时间
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## 🔧 后端API设计

### 目录结构
```
backend/
├── controllers/
│   └── warehouseController.js          # 仓库管理控制器
├── services/
│   ├── warehouseService.js             # 仓库业务服务
│   ├── barcodeService.js               # 条码服务
│   └── inventoryService.js             # 库存服务
├── routes/
│   └── warehouseRoutes.js              # 仓库路由
└── middlewares/
    └── warehouseMiddleware.js          # 仓库中间件
```

### 核心API接口

#### 物料管理接口
```javascript
// 物料入库
POST   /api/warehouse/materials/inbound
// 物料发料
POST   /api/warehouse/materials/outbound
// 物料退料
POST   /api/warehouse/materials/return
// 物料查询
GET    /api/warehouse/materials
// 物料详情
GET    /api/warehouse/materials/:id
```

#### 成品管理接口
```javascript
// 成品入库
POST   /api/warehouse/products/inbound
// 成品出库
POST   /api/warehouse/products/outbound
// 成品返仓
POST   /api/warehouse/products/return
// 成品查询
GET    /api/warehouse/products
// 批次管理
GET    /api/warehouse/products/batches
```

#### 条码管理接口
```javascript
// 生成条码
POST   /api/warehouse/barcodes/generate
// 验证条码
POST   /api/warehouse/barcodes/validate
// 条码查询
GET    /api/warehouse/barcodes/:barcode
```

#### 库存管理接口
```javascript
// 库存查询
GET    /api/warehouse/inventory
// 库存预警
GET    /api/warehouse/inventory/alerts
// 库存统计
GET    /api/warehouse/inventory/statistics
```

## 🎨 前端组件设计

### 目录结构
```
frontend/
├── components/warehouse/
│   ├── MaterialManagement.js           # 物料管理组件
│   ├── ProductManagement.js            # 成品管理组件
│   ├── BarcodeScanner.js               # 条码扫描组件
│   ├── InventoryMonitor.js             # 库存监控组件
│   ├── TransactionHistory.js           # 事务历史组件
│   └── WarehouseReports.js             # 报表统计组件
├── js/
│   └── warehouse-management.js         # 仓库管理页面逻辑
├── css/
│   └── warehouse-management.css        # 仓库管理样式
└── pages/
    └── warehouse.html                  # 仓库管理主页面
```

### 核心组件功能

#### 1. 条码扫描组件 (BarcodeScanner.js)
```javascript
const BarcodeScanner = {
    template: `
        <div class="barcode-scanner">
            <div class="scanner-input">
                <input v-model="barcodeInput" @keyup.enter="scanBarcode" 
                       placeholder="扫描或输入条码" class="form-input">
                <button @click="scanBarcode" class="btn-primary">扫描</button>
            </div>
            <div v-if="scanResult" class="scan-result">
                <div class="result-success" v-if="scanResult.valid">
                    ✓ 条码验证成功: {{ scanResult.info }}
                </div>
                <div class="result-error" v-else>
                    ✗ {{ scanResult.error }}
                </div>
            </div>
        </div>
    `,
    // 组件逻辑...
}
```

#### 2. 库存监控组件 (InventoryMonitor.js)
```javascript
const InventoryMonitor = {
    template: `
        <div class="inventory-monitor">
            <div class="monitor-header">
                <h3>库存监控</h3>
                <div class="alert-summary">
                    <span class="alert-count" v-if="alerts.length">
                        {{ alerts.length }} 项预警
                    </span>
                </div>
            </div>
            <div class="inventory-grid">
                <div v-for="item in inventoryItems" :key="item.id" 
                     class="inventory-card" :class="getAlertClass(item)">
                    <div class="item-info">
                        <h4>{{ item.name }}</h4>
                        <p>当前库存: {{ item.current_stock }}</p>
                        <p>安全库存: {{ item.safety_stock }}</p>
                    </div>
                </div>
            </div>
        </div>
    `,
    // 组件逻辑...
}
```

## 🔐 权限配置

### 权限定义
在 `frontend/components/user/PermissionManager.js` 中添加：

```javascript
// 仓库管理权限组
{
    id: 'warehouse_management',
    name: '仓库管理',
    description: '仓库管理相关权限',
    permissions: [
        {
            id: 'warehouse_view',
            name: '查看仓库',
            description: '允许查看仓库信息和库存数据'
        },
        {
            id: 'warehouse_inbound',
            name: '入库操作',
            description: '允许执行物料和成品入库操作'
        },
        {
            id: 'warehouse_outbound',
            name: '出库操作',
            description: '允许执行物料发料和成品出库操作'
        },
        {
            id: 'warehouse_return',
            name: '退料操作',
            description: '允许执行退料和返仓操作'
        },
        {
            id: 'warehouse_manage',
            name: '仓库管理',
            description: '允许管理仓库配置和系统设置'
        }
    ]
}
```

### API权限保护
```javascript
// 在路由中添加权限检查
router.post('/api/warehouse/materials/inbound', 
    authenticateJWT, 
    checkPermission('warehouse_inbound'), 
    warehouseController.materialInbound
);
```

## 📋 实施计划

### 第一阶段：基础架构搭建 (1-2周)
- [ ] 数据库表结构创建
- [ ] 后端基础服务搭建
- [ ] 前端基础组件开发
- [ ] 权限系统集成

### 第二阶段：核心功能开发 (2-3周)
- [ ] 物料管理功能
- [ ] 成品管理功能
- [ ] 条码扫描系统
- [ ] 库存监控功能

### 第三阶段：高级功能开发 (1-2周)
- [ ] 报表统计功能
- [ ] 预警系统
- [ ] 追溯系统
- [ ] 异常处理机制

### 第四阶段：测试与优化 (1周)
- [ ] 功能测试
- [ ] 性能优化
- [ ] 用户体验优化
- [ ] 文档完善

## 🧪 测试方案

### 功能测试
- 物料入库/出库/退料流程测试
- 成品入库/出库/返仓流程测试
- 条码生成/扫描/验证测试
- 库存更新/预警测试

### 集成测试
- 与用户权限系统集成测试
- 与现有模块数据交互测试
- API接口完整性测试

### 性能测试
- 大批量数据处理测试
- 并发操作测试
- 数据库查询性能测试

## 🚀 部署说明

### 数据库迁移
```sql
-- 执行数据库迁移脚本
sqlite3 backend/database/application_system.db < warehouse_schema.sql
```

### 路由注册
在 `backend/routes/index.js` 中添加：
```javascript
const warehouseRoutes = require('./warehouseRoutes');
router.use('/warehouse', warehouseRoutes);
```

### 前端集成
在主页面侧边栏中添加仓库管理入口，并配置相应的权限检查。

---

**注意**: 本实施方案严格遵循当前系统的代码组织原则和开发规范，确保与现有系统的完美集成。
