/**
 * 认证中间件
 * 处理用户认证和授权
 */

const jwt = require('jsonwebtoken');
const config = require('../config');

/**
 * JWT认证中间件
 * 验证请求中的JWT令牌
 */
function authenticateJWT(req, res, next) {
    const authHeader = req.headers.authorization;



    if (authHeader) {
        const token = authHeader.split(' ')[1];

        jwt.verify(token, config.jwt.secret, (err, user) => {
            if (err) {
                return res.status(403).json({
                    success: false,
                    message: '令牌无效或已过期'
                });
            }

            // 检查用户是否被禁用
            if (user.active === false) {
                return res.status(403).json({
                    success: false,
                    message: '账户已被禁用，请联系管理员'
                });
            }

            // 只有admin角色拥有所有权限
            const isSystemAdmin = user.role === 'admin';

            if (isSystemAdmin) {
                // 系统管理员的完整权限列表
                const adminPermissions = [
                    'new_application',
                    'application_record',
                    'pending_approval',
                    'approved_applications',
                    'schedule_view',
                    'schedule_create',
                    'schedule_edit',
                    'schedule_delete',
                    'schedule_execute',
                    'scheduling_manage',
                    'algorithm_tuning',
                    'resource_manage',
                    'schedule_report',
                    'product_view',
                    'product_create',
                    'product_edit',
                    'product_delete',
                    'operator_skill_manage',
                    'equipment_manage',
                    'equipment_info',
                    'equipment_maintenance',
                    'equipment_health',
                    'equipment_edit',
                    'quality_upload',
                    'quality_view',
                    'quality_download',
                    'quality_manage',
                    'file_upload',
                    'file_view',
                    'file_download',
                    'file_manage',
                    'file_confirm',
                    'warehouse_view',
                    'warehouse_inbound',
                    'warehouse_outbound',
                    'warehouse_returns',
                    'warehouse_materials_manage',
                    'warehouse_products_manage',
                    'warehouse_batches_manage',
                    'warehouse_qrcode_generate',
                    'warehouse_reports_view',
                    'warehouse_alerts_manage',
                    'warehouse_config_manage',
                    'warehouse_adjustments',
                    'user_settings',
                    'view_users',
                    'create_user',
                    'edit_user',
                    'delete_user',
                    'manage_permissions'
                ];

                // 确保admin角色有所有权限
                user.permissions = [...new Set([...(user.permissions || []), ...adminPermissions])];
            }

            req.user = user;
            next();
        });
    } else {
        res.status(401).json({
            success: false,
            message: '未提供认证令牌'
        });
    }
}

/**
 * 角色授权中间件
 * 检查用户是否具有指定角色
 * @param {string|Array} roles - 允许的角色
 */
function authorizeRoles(roles) {
    return (req, res, next) => {
        if (!req.user) {
            return res.status(401).json({
                success: false,
                message: '未认证用户'
            });
        }

        const allowedRoles = Array.isArray(roles) ? roles : [roles];

        if (allowedRoles.includes(req.user.role)) {
            next();
        } else {
            res.status(403).json({
                success: false,
                message: '没有权限执行此操作'
            });
        }
    };
}

/**
 * 权限检查中间件
 * 检查用户是否具有指定权限
 * @param {string|Array} permission - 需要的权限（单个权限或权限数组，数组表示需要其中任一权限）
 */
function checkPermission(permission) {
    return (req, res, next) => {
        const user = req.user;

        if (!user) {
            return res.status(401).json({
                success: false,
                message: '未认证用户'
            });
        }

        // 只有admin角色拥有所有权限
        const isSystemAdmin = user.role === 'admin';

        if (isSystemAdmin) {
            return next();
        }

        // 支持单个权限或权限数组
        const requiredPermissions = Array.isArray(permission) ? permission : [permission];
        const userPermissions = user.permissions || [];

        // 检查用户是否有任一所需权限
        const hasPermission = requiredPermissions.some(perm => userPermissions.includes(perm));

        if (hasPermission) {
            return next();
        }

        return res.status(403).json({
            success: false,
            message: '权限不足'
        });
    };
}

module.exports = {
    authenticateJWT,
    authorizeRoles,
    checkPermission
};
