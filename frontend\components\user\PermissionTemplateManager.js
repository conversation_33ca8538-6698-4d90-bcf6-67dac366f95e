/**
 * 权限模板管理组件
 * 用于管理权限模板的创建、编辑、删除和查看
 */

import {
    getPermissionTemplates,
    createPermissionTemplate,
    updatePermissionTemplate,
    deletePermissionTemplate
} from '../../scripts/api/user.js';

export default {
    props: {
        currentUser: Object
    },
    setup(props) {
        const { ref, reactive, onMounted, computed } = Vue;

        // 状态变量
        const templates = ref([]);
        const isLoading = ref(false);
        const searchTerm = ref('');
        const showTemplateModal = ref(false);
        const isEditing = ref(false);
        const isSubmitting = ref(false);
        const selectedTemplate = ref(null);

        // 表单数据
        const templateForm = reactive({
            name: '',
            description: '',
            permissions: []
        });

        // 权限组定义 - 与PermissionManager.js保持一致
        const permissionGroups = ref([
            {
                id: 'applications',
                name: '申请管理',
                permissions: [
                    { id: 'new_application', name: '新建申请', description: '允许用户创建新的申请' },
                    { id: 'application_record', name: '申请记录', description: '允许用户查看自己的申请记录' },
                    { id: 'pending_approval', name: '待审核', description: '允许用户查看待审核的申请' },
                    { id: 'approved_applications', name: '已审核', description: '允许用户查看已审核的申请' }
                ]
            },
            {
                id: 'schedule_management',
                name: '生产排程管理',
                permissions: [
                    { id: 'schedule_view', name: '查看排程', description: '允许用户查看生产排程信息' },
                    { id: 'schedule_create', name: '创建排程', description: '允许用户创建新的生产排程' },
                    { id: 'schedule_edit', name: '编辑排程', description: '允许用户编辑现有排程' },
                    { id: 'schedule_delete', name: '删除排程', description: '允许用户删除排程' },
                    { id: 'schedule_execute', name: '执行排程', description: '允许用户执行排程操作（开始、暂停、完成等）' },
                    { id: 'scheduling_manage', name: '智能排程', description: '允许用户使用智能排程功能' },
                    { id: 'algorithm_tuning', name: '算法调优', description: '允许用户访问算法调优功能（仅限系统管理员）' },
                    { id: 'resource_manage', name: '资源管理', description: '允许用户管理生产资源（设备、人员、物料）' },
                    { id: 'schedule_report', name: '排程报表', description: '允许用户查看排程分析报表' }
                ]
            },
            {
                id: 'product_management',
                name: '产品管理',
                permissions: [
                    { id: 'product_view', name: '查看产品', description: '允许用户查看产品信息' },
                    { id: 'product_create', name: '创建产品', description: '允许用户创建新产品' },
                    { id: 'product_edit', name: '编辑产品', description: '允许用户编辑产品信息和工艺流程' },
                    { id: 'product_delete', name: '删除产品', description: '允许用户删除产品' },
                    { id: 'operator_skill_manage', name: '操作员技能管理', description: '允许用户管理操作员技能' }
                ]
            },
            {
                id: 'equipment_management',
                name: '设备管理',
                permissions: [
                    { id: 'equipment_info', name: '设备信息', description: '允许用户查看设备信息' },
                    { id: 'equipment_manage', name: '设备管理', description: '允许用户管理设备' },
                    { id: 'equipment_maintenance', name: '维修保养', description: '允许用户管理设备维修保养记录' },
                    { id: 'equipment_health', name: '设备健康度', description: '允许用户查看设备健康度评估' },
                    { id: 'equipment_edit', name: '设备产能管理', description: '允许用户管理设备产能配置' }
                ]
            },
            {
                id: 'quality_management',
                name: '质量管理',
                permissions: [
                    { id: 'quality_upload', name: '上传检测报告', description: '允许用户上传检测报告' },
                    { id: 'quality_view', name: '查看检测报告', description: '允许用户查看检测报告列表' },
                    { id: 'quality_download', name: '下载检测报告', description: '允许用户下载检测报告' },
                    { id: 'quality_manage', name: '质量管理', description: '允许用户管理质量相关功能' }
                ]
            },
            {
                id: 'file_management',
                name: '文件管理',
                permissions: [
                    { id: 'file_upload', name: '上传文件', description: '允许用户上传客户二认文件' },
                    { id: 'file_view', name: '查看文件', description: '允许用户查看文件列表和详情' },
                    { id: 'file_download', name: '下载文件', description: '允许用户下载文件' },
                    { id: 'file_manage', name: '文件管理', description: '允许用户管理文件相关功能' },
                    { id: 'file_confirm', name: '确认收到', description: '允许用户确认收到文件变更通知' }
                ]
            },
            {
                id: 'user_management',
                name: '用户管理',
                permissions: [
                    { id: 'view_users', name: '查看用户', description: '允许用户查看用户列表' },
                    { id: 'create_user', name: '创建用户', description: '允许用户创建新用户' },
                    { id: 'edit_user', name: '编辑用户', description: '允许用户编辑现有用户' },
                    { id: 'delete_user', name: '删除用户', description: '允许用户删除用户' },
                    { id: 'manage_permissions', name: '管理权限', description: '允许用户管理其他用户的权限' }
                ]
            },
            {
                id: 'settings',
                name: '设置',
                permissions: [
                    { id: 'user_settings', name: '个人设置', description: '允许用户修改个人设置' }
                ]
            }
        ]);

        // 计算属性
        const filteredTemplates = computed(() => {
            if (!searchTerm.value) return templates.value;

            return templates.value.filter(template =>
                template.name.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                template.description.toLowerCase().includes(searchTerm.value.toLowerCase())
            );
        });

        const availablePermissions = computed(() => {
            return permissionGroups.value.flatMap(group => group.permissions);
        });

        const canManageTemplates = computed(() => {
            // 只有admin角色可以管理权限模板
            return props.currentUser && props.currentUser.role === 'admin';
        });

        // 生命周期
        onMounted(() => {
            loadTemplates(true); // 组件初始化时强制刷新，确保获取最新数据
        });

        // 方法
        async function loadTemplates(forceRefresh = false) {
            try {
                isLoading.value = true;
                const response = await getPermissionTemplates(forceRefresh);
                if (response.success) {
                    templates.value = response.templates;
                }
            } catch (error) {
                console.error('加载权限模板失败:', error);
                alert('加载权限模板失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isLoading.value = false;
            }
        }

        function openCreateModal() {
            resetForm();
            isEditing.value = false;
            showTemplateModal.value = true;
        }

        function openEditModal(template) {
            resetForm();
            templateForm.name = template.name;
            templateForm.description = template.description;
            templateForm.permissions = [...template.permissions];
            selectedTemplate.value = template;
            isEditing.value = true;
            showTemplateModal.value = true;
        }

        function closeModal() {
            showTemplateModal.value = false;
            resetForm();
            selectedTemplate.value = null;
        }

        function resetForm() {
            templateForm.name = '';
            templateForm.description = '';
            templateForm.permissions = [];
        }

        async function saveTemplate() {
            if (!templateForm.name.trim()) {
                alert('请输入模板名称');
                return;
            }

            try {
                isSubmitting.value = true;

                const templateData = {
                    name: templateForm.name.trim(),
                    description: templateForm.description.trim(),
                    permissions: templateForm.permissions
                };

                let response;
                if (isEditing.value) {
                    response = await updatePermissionTemplate(selectedTemplate.value.id, templateData);
                } else {
                    response = await createPermissionTemplate(templateData);
                }

                if (response.success) {
                    if (isEditing.value && selectedTemplate.value?.isBuiltIn) {
                        alert('预设模板已转换为自定义模板并保存成功');
                    } else {
                        alert(isEditing.value ? '权限模板更新成功' : '权限模板创建成功');
                    }
                    closeModal();
                    await loadTemplates(true); // 强制刷新，确保获取最新数据
                } else {
                    alert((isEditing.value ? '更新' : '创建') + '失败: ' + response.message);
                }
            } catch (error) {
                console.error('保存权限模板失败:', error);
                alert('保存失败: ' + (error.response?.data?.message || error.message));
            } finally {
                isSubmitting.value = false;
            }
        }

        async function deleteTemplate(template) {
            if (template.isBuiltIn) {
                alert('预设模板不能删除，只能编辑修改');
                return;
            }

            if (!confirm(`确定要删除权限模板"${template.name}"吗？此操作不可恢复。`)) {
                return;
            }

            try {
                const response = await deletePermissionTemplate(template.id);
                if (response.success) {
                    alert('权限模板删除成功');
                    await loadTemplates(true); // 强制刷新，确保获取最新数据
                } else {
                    alert('删除失败: ' + response.message);
                }
            } catch (error) {
                console.error('删除权限模板失败:', error);
                alert('删除失败: ' + (error.response?.data?.message || error.message));
            }
        }

        function hasPermission(permissionId) {
            return templateForm.permissions.includes(permissionId);
        }

        function togglePermission(permissionId) {
            if (hasPermission(permissionId)) {
                templateForm.permissions = templateForm.permissions.filter(id => id !== permissionId);
            } else {
                templateForm.permissions.push(permissionId);
            }
        }

        function toggleGroupPermissions(groupId, enabled) {
            const group = permissionGroups.value.find(g => g.id === groupId);
            if (!group) return;

            const permissionIds = group.permissions.map(p => p.id);

            if (enabled) {
                // 添加组内所有权限
                permissionIds.forEach(id => {
                    if (!templateForm.permissions.includes(id)) {
                        templateForm.permissions.push(id);
                    }
                });
            } else {
                // 移除组内所有权限
                templateForm.permissions = templateForm.permissions.filter(id => !permissionIds.includes(id));
            }
        }

        function isGroupFullySelected(groupId) {
            const group = permissionGroups.value.find(g => g.id === groupId);
            if (!group) return false;

            return group.permissions.every(p => templateForm.permissions.includes(p.id));
        }

        function isGroupPartiallySelected(groupId) {
            const group = permissionGroups.value.find(g => g.id === groupId);
            if (!group) return false;

            const selectedCount = group.permissions.filter(p => templateForm.permissions.includes(p.id)).length;
            return selectedCount > 0 && selectedCount < group.permissions.length;
        }

        function getPermissionName(permissionId) {
            const permission = availablePermissions.value.find(p => p.id === permissionId);
            return permission ? permission.name : permissionId;
        }

        function formatDate(dateString) {
            return new Date(dateString).toLocaleString('zh-CN');
        }

        return {
            templates,
            isLoading,
            searchTerm,
            showTemplateModal,
            isEditing,
            isSubmitting,
            selectedTemplate,
            templateForm,
            permissionGroups,
            availablePermissions,
            filteredTemplates,
            canManageTemplates,

            // 方法
            openCreateModal,
            openEditModal,
            closeModal,
            saveTemplate,
            deleteTemplate,
            hasPermission,
            togglePermission,
            toggleGroupPermissions,
            isGroupFullySelected,
            isGroupPartiallySelected,
            getPermissionName,
            formatDate
        };
    },

    template: `
        <div class="bg-white rounded-lg shadow-md">
            <!-- 头部 -->
            <div class="p-6 border-b border-gray-200">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-xl font-semibold text-gray-800">权限模板管理</h2>
                    <button v-if="canManageTemplates"
                            @click="openCreateModal"
                            class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md transition-colors">
                        <i class="fas fa-plus mr-2"></i>新建模板
                    </button>
                    <div v-else class="text-gray-500 text-sm">
                        需要管理员权限才能管理模板
                    </div>
                </div>


                <!-- 搜索框 -->
                <div class="relative w-64">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                    <input v-model="searchTerm"
                           type="text"
                           placeholder="搜索模板名称或描述..."
                           class="pl-10 pr-8 py-1.5 w-full border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <button v-if="searchTerm" @click="searchTerm = ''" class="absolute inset-y-0 right-0 pr-2 flex items-center text-gray-400 hover:text-gray-600">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- 模板列表 -->
            <div class="p-6">
                <div v-if="isLoading" class="text-center py-8">
                    <i class="fas fa-spinner fa-spin text-2xl text-gray-400"></i>
                    <p class="text-gray-500 mt-2">加载中...</p>
                </div>

                <div v-else-if="filteredTemplates.length === 0" class="text-center py-8">
                    <i class="fas fa-folder-open text-4xl text-gray-300 mb-4"></i>
                    <p class="text-gray-500">{{ searchTerm ? '没有找到匹配的模板' : '暂无权限模板' }}</p>
                </div>

                <div v-else class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                    <div v-for="template in filteredTemplates"
                         :key="template.id"
                         class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">

                        <!-- 模板头部 -->
                        <div class="flex justify-between items-start mb-3">
                            <div class="flex-1">
                                <div class="flex items-center gap-2 mb-1">
                                    <h3 class="font-semibold text-gray-800">{{ template.name }}</h3>
                                    <span v-if="template.isBuiltIn"
                                          class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">
                                        预设
                                    </span>
                                </div>
                                <p v-if="template.description"
                                   class="text-sm text-gray-600 mb-2">{{ template.description }}</p>
                            </div>

                            <!-- 操作按钮 -->
                            <div v-if="canManageTemplates" class="flex space-x-2 ml-2">
                                <button @click="openEditModal(template)"
                                        class="bg-blue-500 hover:bg-blue-600 text-white px-2 py-1 rounded text-xs"
                                        title="编辑">
                                    <i class="fas fa-edit mr-1"></i>编辑
                                </button>
                                <button @click="deleteTemplate(template)"
                                        :class="template.isBuiltIn ? 'bg-gray-400 cursor-not-allowed' : 'bg-red-500 hover:bg-red-600'"
                                        class="text-white px-2 py-1 rounded text-xs"
                                        :title="template.isBuiltIn ? '预设模板不能删除' : '删除'">
                                    <i class="fas fa-trash mr-1"></i>删除
                                </button>
                            </div>
                            <div v-else class="text-gray-400 text-xs ml-2">
                                需要管理员权限
                            </div>
                        </div>

                        <!-- 权限数量 -->
                        <div class="flex items-center text-sm text-gray-500 mb-3">
                            <i class="fas fa-key mr-1"></i>
                            <span>{{ template.permissions.length }} 个权限</span>
                        </div>

                        <!-- 权限预览 -->
                        <div class="mb-3">
                            <div class="flex flex-wrap gap-1">
                                <span v-for="permissionId in template.permissions.slice(0, 3)"
                                      :key="permissionId"
                                      class="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded">
                                    {{ getPermissionName(permissionId) }}
                                </span>
                                <span v-if="template.permissions.length > 3"
                                      class="inline-block bg-gray-100 text-gray-600 text-xs px-2 py-1 rounded">
                                    +{{ template.permissions.length - 3 }}
                                </span>
                            </div>
                        </div>

                        <!-- 创建时间 -->
                        <div class="text-xs text-gray-400">
                            创建于 {{ formatDate(template.createdAt) }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 模板编辑模态框 -->
        <div v-if="showTemplateModal"
             class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
             @click.self="closeModal">
            <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden">
                <!-- 模态框头部 -->
                <div class="flex justify-between items-center p-6 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-800">
                        {{ isEditing ? (selectedTemplate?.isBuiltIn ? '编辑预设模板（将创建副本）' : '编辑权限模板') : '新建权限模板' }}
                    </h3>
                    <button @click="closeModal" class="text-gray-400 hover:text-gray-600">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- 模态框内容 -->
                <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                    <!-- 基本信息 -->
                    <div class="mb-6">
                        <h4 class="text-md font-semibold text-gray-800 mb-4">基本信息</h4>

                        <div class="grid gap-4 md:grid-cols-2">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">
                                    模板名称 <span class="text-red-500">*</span>
                                </label>
                                <input v-model="templateForm.name"
                                       type="text"
                                       placeholder="请输入模板名称"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">描述</label>
                                <input v-model="templateForm.description"
                                       type="text"
                                       placeholder="请输入模板描述"
                                       class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- 权限配置 -->
                    <div>
                        <h4 class="text-md font-semibold text-gray-800 mb-4">权限配置</h4>

                        <div class="space-y-6">
                            <div v-for="group in permissionGroups" :key="group.id" class="border border-gray-200 rounded-lg p-4">
                                <!-- 权限组头部 -->
                                <div class="flex items-center justify-between mb-4">
                                    <h5 class="font-medium text-gray-800">{{ group.name }}</h5>
                                    <div class="flex items-center space-x-2">
                                        <label class="flex items-center">
                                            <input type="checkbox"
                                                   :checked="isGroupFullySelected(group.id)"
                                                   :indeterminate="isGroupPartiallySelected(group.id)"
                                                   @change="toggleGroupPermissions(group.id, $event.target.checked)"
                                                   class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                            <span class="ml-2 text-sm text-gray-600">全选</span>
                                        </label>
                                    </div>
                                </div>

                                <!-- 权限列表 -->
                                <div class="grid gap-3 md:grid-cols-2">
                                    <label v-for="permission in group.permissions"
                                           :key="permission.id"
                                           class="flex items-start space-x-3 p-3 rounded-md hover:bg-gray-50 cursor-pointer">
                                        <input type="checkbox"
                                               :checked="hasPermission(permission.id)"
                                               @change="togglePermission(permission.id)"
                                               class="mt-1 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                        <div class="flex-1">
                                            <div class="font-medium text-gray-800">{{ permission.name }}</div>
                                            <div class="text-sm text-gray-600">{{ permission.description }}</div>
                                        </div>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模态框底部 -->
                <div class="p-6 border-t border-gray-200">
                    <!-- 预设模板提示 -->
                    <div v-if="isEditing && selectedTemplate?.isBuiltIn"
                         class="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-yellow-600 mt-0.5 mr-2"></i>
                            <div class="text-sm text-yellow-800">
                                <strong>注意：</strong>编辑预设模板将创建一个新的自定义模板副本，原预设模板保持不变。
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-3">
                        <button @click="closeModal"
                                class="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors">
                            取消
                        </button>
                        <button @click="saveTemplate"
                                :disabled="isSubmitting"
                                class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-md transition-colors disabled:opacity-50">
                            <i v-if="isSubmitting" class="fas fa-spinner fa-spin mr-2"></i>
                            {{ isEditing ? (selectedTemplate?.isBuiltIn ? '创建副本' : '更新') : '创建' }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `
};
