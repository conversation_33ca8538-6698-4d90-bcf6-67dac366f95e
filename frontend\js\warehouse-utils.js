/**
 * 仓库管理工具函数
 * 提供通用的工具方法和常量
 */

/**
 * 常量定义
 */
const WAREHOUSE_CONSTANTS = {
    // 物料类型
    ITEM_TYPES: {
        MATERIAL: 'material',
        PRODUCT: 'product'
    },

    // 入库类型
    INBOUND_TYPES: {
        PURCHASE: 'purchase',
        PRODUCTION: 'production',
        RETURN: 'return',
        TRANSFER: 'transfer'
    },

    // 出库类型
    OUTBOUND_TYPES: {
        PRODUCTION: 'production',
        SALE: 'sale',
        RETURN: 'return',
        TRANSFER: 'transfer'
    },

    // 退料类型
    RETURN_TYPES: {
        WORKSHOP_RETURN: 'workshop_return',
        SUPPLIER_RETURN: 'supplier_return',
        CUSTOMER_RETURN: 'customer_return'
    },

    // 退料原因
    RETURN_REASONS: {
        EXCESS: 'excess',
        QUALITY_ISSUE: 'quality_issue',
        SPECIFICATION_MISMATCH: 'specification_mismatch',
        QUANTITY_ERROR: 'quantity_error',
        OTHER: 'other'
    },

    // 二维码类型
    QR_TYPES: {
        MATERIAL_STANDARD: 'material_standard',
        PRODUCT_STANDARD: 'product_standard',
        CUSTOMER_SUPPLIED: 'customer_supplied'
    },

    // 库存状态
    STOCK_STATUS: {
        OUT_OF_STOCK: 'out_of_stock',
        LOW_STOCK: 'low_stock',
        NORMAL: 'normal',
        OVERSTOCK: 'overstock'
    }
};

/**
 * 数据验证工具
 */
const ValidationUtils = {
    /**
     * 验证必填字段
     * @param {Object} data 数据对象
     * @param {Array} requiredFields 必填字段列表
     * @returns {Object} 验证结果
     */
    validateRequired(data, requiredFields) {
        const errors = [];
        
        for (const field of requiredFields) {
            if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {
                errors.push(`${field} 不能为空`);
            }
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    },

    /**
     * 验证数量
     * @param {number} quantity 数量
     * @param {string} fieldName 字段名
     * @returns {Object} 验证结果
     */
    validateQuantity(quantity, fieldName = '数量') {
        const errors = [];
        
        if (quantity === null || quantity === undefined) {
            errors.push(`${fieldName}不能为空`);
        } else if (isNaN(quantity) || quantity < 0) {
            errors.push(`${fieldName}必须为非负数`);
        } else if (!Number.isInteger(Number(quantity))) {
            errors.push(`${fieldName}必须为整数`);
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    },

    /**
     * 验证物料编码
     * @param {string} code 物料编码
     * @returns {Object} 验证结果
     */
    validateItemCode(code) {
        const errors = [];
        
        if (!code || typeof code !== 'string') {
            errors.push('物料编码不能为空');
        } else if (!/^[A-Z0-9-_]+$/.test(code)) {
            errors.push('物料编码只能包含大写字母、数字、连字符和下划线');
        } else if (code.length > 50) {
            errors.push('物料编码长度不能超过50个字符');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
};

/**
 * 格式化工具
 */
const FormatUtils = {
    /**
     * 格式化日期时间
     * @param {string|Date} date 日期
     * @param {string} format 格式类型
     * @returns {string} 格式化后的日期
     */
    formatDateTime(date, format = 'full') {
        if (!date) return '-';
        
        const d = new Date(date);
        if (isNaN(d.getTime())) return '-';
        
        switch (format) {
            case 'date':
                return d.toLocaleDateString('zh-CN');
            case 'time':
                return d.toLocaleTimeString('zh-CN');
            case 'short':
                return d.toLocaleDateString('zh-CN') + ' ' + d.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
            default:
                return d.toLocaleString('zh-CN');
        }
    },

    /**
     * 格式化数量
     * @param {number} quantity 数量
     * @param {string} unit 单位
     * @param {boolean} showUnit 是否显示单位
     * @returns {string} 格式化后的数量
     */
    formatQuantity(quantity, unit = 'pcs', showUnit = true) {
        if (quantity === null || quantity === undefined) return '-';
        
        const formattedNumber = Number(quantity).toLocaleString('zh-CN');
        return showUnit ? `${formattedNumber} ${unit}` : formattedNumber;
    },

    /**
     * 格式化文件大小
     * @param {number} bytes 字节数
     * @returns {string} 格式化后的大小
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    /**
     * 格式化百分比
     * @param {number} value 数值
     * @param {number} total 总数
     * @param {number} decimals 小数位数
     * @returns {string} 百分比字符串
     */
    formatPercentage(value, total, decimals = 1) {
        if (!total || total === 0) return '0%';
        
        const percentage = (value / total * 100).toFixed(decimals);
        return `${percentage}%`;
    }
};

/**
 * 状态工具
 */
const StatusUtils = {
    /**
     * 获取库存状态
     * @param {number} current 当前库存
     * @param {number} safety 安全库存
     * @param {number} max 最大库存
     * @returns {Object} 状态信息
     */
    getStockStatus(current, safety = 0, max = 0) {
        let status, color, text;
        
        if (current <= 0) {
            status = WAREHOUSE_CONSTANTS.STOCK_STATUS.OUT_OF_STOCK;
            color = 'text-red-600 bg-red-50';
            text = '缺货';
        } else if (current <= safety) {
            status = WAREHOUSE_CONSTANTS.STOCK_STATUS.LOW_STOCK;
            color = 'text-yellow-600 bg-yellow-50';
            text = '低库存';
        } else if (max > 0 && current >= max) {
            status = WAREHOUSE_CONSTANTS.STOCK_STATUS.OVERSTOCK;
            color = 'text-purple-600 bg-purple-50';
            text = '超储';
        } else {
            status = WAREHOUSE_CONSTANTS.STOCK_STATUS.NORMAL;
            color = 'text-green-600 bg-green-50';
            text = '正常';
        }
        
        return { status, color, text };
    },

    /**
     * 获取操作状态样式
     * @param {string} status 状态
     * @returns {Object} 样式信息
     */
    getOperationStatusStyle(status) {
        const statusMap = {
            'pending': { color: 'text-yellow-600 bg-yellow-50', text: '待处理' },
            'processing': { color: 'text-blue-600 bg-blue-50', text: '处理中' },
            'completed': { color: 'text-green-600 bg-green-50', text: '已完成' },
            'cancelled': { color: 'text-red-600 bg-red-50', text: '已取消' },
            'failed': { color: 'text-red-600 bg-red-50', text: '失败' }
        };
        
        return statusMap[status] || { color: 'text-gray-600 bg-gray-50', text: '未知' };
    }
};

/**
 * 二维码工具
 */
const QRCodeUtils = {
    /**
     * 解析二维码信息
     * @param {string} qrCode 二维码
     * @returns {Object} 解析结果
     */
    parseQRCode(qrCode) {
        if (!qrCode || typeof qrCode !== 'string') {
            return { isValid: false, message: '二维码格式错误' };
        }
        
        const parts = qrCode.split('-');
        
        if (parts.length < 4) {
            return { isValid: false, message: '二维码格式不完整' };
        }
        
        const type = parts[0];
        let result = { isValid: true, type: type.toLowerCase() };
        
        switch (type) {
            case 'MAT':
                result.supplierCode = parts[1];
                result.itemCode = parts[2];
                result.batchNumber = parts[3];
                result.serialNumber = parts[4];
                result.description = '物料二维码';
                break;
                
            case 'PRD':
                result.itemCode = parts[1];
                result.productionDate = parts[2];
                result.batchNumber = parts[3];
                result.boxNumber = parts[4];
                result.description = '成品二维码';
                break;
                
            case 'CUST':
                result.customerCode = parts[1];
                result.itemCode = parts[2];
                result.batchNumber = parts[3];
                result.serialNumber = parts[4];
                result.description = '客供物料二维码';
                break;
                
            default:
                return { isValid: false, message: '不支持的二维码类型' };
        }
        
        return result;
    },

    /**
     * 生成二维码显示内容
     * @param {string} qrCode 二维码
     * @returns {string} 显示内容
     */
    generateDisplayText(qrCode) {
        const parsed = this.parseQRCode(qrCode);
        
        if (!parsed.isValid) {
            return qrCode;
        }
        
        switch (parsed.type) {
            case 'mat':
                return `物料: ${parsed.itemCode} (${parsed.supplierCode})`;
            case 'prd':
                return `成品: ${parsed.itemCode} (${parsed.productionDate})`;
            case 'cust':
                return `客供: ${parsed.itemCode} (${parsed.customerCode})`;
            default:
                return qrCode;
        }
    }
};

/**
 * 表格工具
 */
const TableUtils = {
    /**
     * 生成分页信息
     * @param {number} page 当前页
     * @param {number} limit 每页数量
     * @param {number} total 总数
     * @returns {Object} 分页信息
     */
    generatePagination(page, limit, total) {
        const totalPages = Math.ceil(total / limit);
        const startItem = (page - 1) * limit + 1;
        const endItem = Math.min(page * limit, total);
        
        return {
            page,
            limit,
            total,
            totalPages,
            startItem,
            endItem,
            hasPrev: page > 1,
            hasNext: page < totalPages
        };
    },

    /**
     * 排序数据
     * @param {Array} data 数据数组
     * @param {string} field 排序字段
     * @param {string} direction 排序方向 (asc/desc)
     * @returns {Array} 排序后的数据
     */
    sortData(data, field, direction = 'asc') {
        return [...data].sort((a, b) => {
            let aVal = a[field];
            let bVal = b[field];
            
            // 处理日期
            if (typeof aVal === 'string' && aVal.includes('T')) {
                aVal = new Date(aVal);
                bVal = new Date(bVal);
            }
            
            // 处理数字
            if (typeof aVal === 'string' && !isNaN(aVal)) {
                aVal = Number(aVal);
                bVal = Number(bVal);
            }
            
            if (direction === 'asc') {
                return aVal > bVal ? 1 : -1;
            } else {
                return aVal < bVal ? 1 : -1;
            }
        });
    },

    /**
     * 过滤数据
     * @param {Array} data 数据数组
     * @param {string} searchTerm 搜索词
     * @param {Array} searchFields 搜索字段
     * @returns {Array} 过滤后的数据
     */
    filterData(data, searchTerm, searchFields = []) {
        if (!searchTerm || searchTerm.trim() === '') {
            return data;
        }
        
        const term = searchTerm.toLowerCase();
        
        return data.filter(item => {
            if (searchFields.length === 0) {
                // 搜索所有字段
                return Object.values(item).some(value => 
                    String(value).toLowerCase().includes(term)
                );
            } else {
                // 搜索指定字段
                return searchFields.some(field => 
                    String(item[field] || '').toLowerCase().includes(term)
                );
            }
        });
    }
};

/**
 * 本地存储工具
 */
const StorageUtils = {
    /**
     * 保存到本地存储
     * @param {string} key 键名
     * @param {any} value 值
     */
    save(key, value) {
        try {
            localStorage.setItem(`warehouse_${key}`, JSON.stringify(value));
        } catch (error) {
            console.error('保存到本地存储失败:', error);
        }
    },

    /**
     * 从本地存储读取
     * @param {string} key 键名
     * @param {any} defaultValue 默认值
     * @returns {any} 读取的值
     */
    load(key, defaultValue = null) {
        try {
            const item = localStorage.getItem(`warehouse_${key}`);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('从本地存储读取失败:', error);
            return defaultValue;
        }
    },

    /**
     * 删除本地存储项
     * @param {string} key 键名
     */
    remove(key) {
        try {
            localStorage.removeItem(`warehouse_${key}`);
        } catch (error) {
            console.error('删除本地存储项失败:', error);
        }
    }
};

// 导出工具对象
window.WarehouseUtils = {
    Constants: WAREHOUSE_CONSTANTS,
    Validation: ValidationUtils,
    Format: FormatUtils,
    Status: StatusUtils,
    QRCode: QRCodeUtils,
    Table: TableUtils,
    Storage: StorageUtils
};
