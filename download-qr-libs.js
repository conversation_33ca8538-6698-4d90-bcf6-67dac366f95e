/**
 * 下载二维码相关库到本地
 * 确保系统可以离线运行
 */

const https = require('https');
const fs = require('fs');
const path = require('path');

// 需要下载的库
const libraries = [
    {
        name: 'qrcode.min.js',
        url: 'https://unpkg.com/qrcode@1.5.3/build/qrcode.min.js',
        description: '二维码生成库'
    },
    {
        name: 'html5-qrcode.min.js',
        url: 'https://unpkg.com/html5-qrcode@2.3.8/minified/html5-qrcode.min.js',
        description: '二维码扫描库'
    }
];

// 目标目录
const targetDir = path.join(__dirname, 'frontend', 'js', 'libs');

// 确保目录存在
if (!fs.existsSync(targetDir)) {
    fs.mkdirSync(targetDir, { recursive: true });
}

/**
 * 下载文件
 * @param {string} url 下载地址
 * @param {string} filename 文件名
 * @returns {Promise}
 */
function downloadFile(url, filename) {
    return new Promise((resolve, reject) => {
        const filePath = path.join(targetDir, filename);
        
        // 检查文件是否已存在
        if (fs.existsSync(filePath)) {
            console.log(`✅ ${filename} 已存在，跳过下载`);
            resolve();
            return;
        }
        
        console.log(`📥 开始下载 ${filename}...`);
        
        const file = fs.createWriteStream(filePath);
        
        https.get(url, (response) => {
            if (response.statusCode !== 200) {
                reject(new Error(`下载失败: ${response.statusCode} ${response.statusMessage}`));
                return;
            }
            
            response.pipe(file);
            
            file.on('finish', () => {
                file.close();
                console.log(`✅ ${filename} 下载完成`);
                resolve();
            });
            
        }).on('error', (error) => {
            fs.unlink(filePath, () => {}); // 删除不完整的文件
            reject(error);
        });
    });
}

/**
 * 下载所有库
 */
async function downloadAllLibraries() {
    console.log('🚀 开始下载二维码相关库...\n');
    
    for (const lib of libraries) {
        try {
            console.log(`📦 ${lib.description}: ${lib.name}`);
            await downloadFile(lib.url, lib.name);
            console.log('');
        } catch (error) {
            console.error(`❌ 下载 ${lib.name} 失败:`, error.message);
            console.log('');
        }
    }
    
    console.log('🎉 所有库下载完成！');
    
    // 验证文件
    console.log('\n📋 验证下载的文件:');
    for (const lib of libraries) {
        const filePath = path.join(targetDir, lib.name);
        if (fs.existsSync(filePath)) {
            const stats = fs.statSync(filePath);
            console.log(`✅ ${lib.name} - ${(stats.size / 1024).toFixed(2)} KB`);
        } else {
            console.log(`❌ ${lib.name} - 文件不存在`);
        }
    }
}

// 执行下载
if (require.main === module) {
    downloadAllLibraries().catch(console.error);
}

module.exports = { downloadAllLibraries };
