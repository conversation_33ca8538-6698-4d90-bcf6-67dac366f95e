/**
 * 二维码服务
 * 处理二维码生成、验证和解析
 */

const logger = require('../../../utils/logger');
const { v4: uuidv4 } = require('uuid');

class QRCodeService {
    constructor(db) {
        this.db = db;
        this.initializeStatements();
    }

    /**
     * 初始化预编译SQL语句
     */
    initializeStatements() {
        this.statements = {
            getQRCode: this.db.prepare('SELECT * FROM warehouse_qrcodes WHERE qr_code = ?'),
            insertQRCode: this.db.prepare(`
                INSERT INTO warehouse_qrcodes 
                (id, qr_code, qr_type, item_type, item_id, item_code, batch_id, 
                 supplier_code, generation_rule, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
            `),
            updateQRCodeUsage: this.db.prepare(`
                UPDATE warehouse_qrcodes 
                SET is_used = ?, first_scan_time = ?, scan_count = scan_count + 1, 
                    status = ?, updated_at = CURRENT_TIMESTAMP
                WHERE qr_code = ?
            `),
            getSystemConfig: this.db.prepare('SELECT config_value FROM warehouse_system_config WHERE config_key = ?')
        };
    }

    /**
     * 生成二维码
     * @param {Object} qrData 二维码数据
     * @returns {Object} 生成结果
     */
    async generateQRCode(qrData) {
        try {
            const { itemType, itemId, itemCode, itemName, batchId, supplierCode, qrType } = qrData;
            
            // 根据类型生成二维码
            let qrCode;
            let generationRule;
            
            switch (qrType) {
                case 'material_standard':
                    qrCode = this.generateMaterialQRCode(itemCode, batchId, supplierCode);
                    generationRule = 'MAT-{supplier}-{material}-{batch}-{serial}';
                    break;
                    
                case 'product_standard':
                    qrCode = this.generateProductQRCode(itemCode, batchId);
                    generationRule = 'PRD-{product}-{date}-{batch}-{box}';
                    break;
                    
                case 'customer_supplied':
                    qrCode = this.generateCustomerSuppliedQRCode(itemCode, batchId, supplierCode);
                    generationRule = 'CUST-{customer}-{material}-{batch}-{serial}';
                    break;
                    
                default:
                    throw new Error('不支持的二维码类型');
            }
            
            // 检查二维码是否已存在
            const existingQR = this.statements.getQRCode.get(qrCode);
            if (existingQR) {
                throw new Error('二维码已存在');
            }
            
            // 保存二维码记录
            const qrId = uuidv4();
            this.statements.insertQRCode.run(
                qrId, qrCode, qrType, itemType, itemId, itemCode, batchId,
                supplierCode, generationRule
            );
            
            logger.info('二维码生成成功', { qrCode, itemCode });
            
            return {
                success: true,
                qrCode,
                qrId,
                generationRule,
                message: '二维码生成成功'
            };
            
        } catch (error) {
            logger.error('二维码生成失败:', error);
            throw error;
        }
    }

    /**
     * 批量生成二维码
     * @param {Object} batchData 批量数据
     * @returns {Object} 生成结果
     */
    async generateBatchQRCodes(batchData) {
        try {
            const { itemType, itemId, itemCode, itemName, batchId, supplierCode, qrType, quantity } = batchData;
            
            const qrCodes = [];
            const errors = [];
            
            for (let i = 1; i <= quantity; i++) {
                try {
                    const qrData = {
                        itemType,
                        itemId,
                        itemCode,
                        itemName,
                        batchId,
                        supplierCode,
                        qrType,
                        serialNumber: i.toString().padStart(4, '0')
                    };
                    
                    const result = await this.generateQRCode(qrData);
                    qrCodes.push(result.qrCode);
                    
                } catch (error) {
                    errors.push(`序号${i}: ${error.message}`);
                }
            }
            
            return {
                success: errors.length === 0,
                qrCodes,
                successCount: qrCodes.length,
                errorCount: errors.length,
                errors,
                message: `批量生成完成，成功${qrCodes.length}个，失败${errors.length}个`
            };
            
        } catch (error) {
            logger.error('批量生成二维码失败:', error);
            throw error;
        }
    }

    /**
     * 验证二维码
     * @param {string} qrCode 二维码
     * @param {Object} context 验证上下文
     * @returns {Object} 验证结果
     */
    async validateQRCode(qrCode, context = {}) {
        try {
            // 1. 格式验证
            const formatValidation = this.validateQRCodeFormat(qrCode);
            if (!formatValidation.isValid) {
                return {
                    success: false,
                    isValid: false,
                    message: formatValidation.message,
                    errors: formatValidation.errors
                };
            }
            
            // 2. 数据库验证
            const qrInfo = this.statements.getQRCode.get(qrCode);
            
            if (!qrInfo) {
                // 新二维码，解析信息
                const parseResult = this.parseQRCode(qrCode);
                return {
                    success: true,
                    isValid: true,
                    isNew: true,
                    parsedInfo: parseResult,
                    message: '二维码格式正确，可以使用'
                };
            }
            
            // 3. 使用状态验证
            if (qrInfo.is_used && context.operation === 'inbound') {
                return {
                    success: false,
                    isValid: false,
                    message: '二维码已被使用',
                    qrInfo
                };
            }
            
            // 4. 业务逻辑验证
            if (context.itemCode && qrInfo.item_code !== context.itemCode) {
                return {
                    success: false,
                    isValid: false,
                    message: '二维码与当前物料不匹配',
                    qrInfo
                };
            }
            
            return {
                success: true,
                isValid: true,
                isNew: false,
                qrInfo,
                message: '二维码验证通过'
            };
            
        } catch (error) {
            logger.error('二维码验证失败:', error);
            return {
                success: false,
                isValid: false,
                message: '二维码验证失败',
                error: error.message
            };
        }
    }

    /**
     * 标记二维码为已使用
     * @param {string} qrCode 二维码
     * @returns {Object} 操作结果
     */
    async markQRCodeUsed(qrCode) {
        try {
            const scanTime = new Date().toISOString();
            this.statements.updateQRCodeUsage.run(1, scanTime, 'used', qrCode);
            
            logger.info('二维码标记为已使用', { qrCode });
            
            return {
                success: true,
                message: '二维码已标记为使用'
            };
            
        } catch (error) {
            logger.error('标记二维码失败:', error);
            throw error;
        }
    }

    /**
     * 生成物料二维码
     * @param {string} itemCode 物料编码
     * @param {string} batchId 批次ID
     * @param {string} supplierCode 供应商代码
     * @returns {string} 二维码
     */
    generateMaterialQRCode(itemCode, batchId, supplierCode) {
        const timestamp = Date.now().toString().slice(-6);
        const serial = Math.random().toString(36).substr(2, 4).toUpperCase();
        return `MAT-${supplierCode || 'SUP'}-${itemCode}-${batchId || timestamp}-${serial}`;
    }

    /**
     * 生成成品二维码
     * @param {string} itemCode 成品编码
     * @param {string} batchId 批次ID
     * @returns {string} 二维码
     */
    generateProductQRCode(itemCode, batchId) {
        const date = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        const boxNumber = Math.random().toString(36).substr(2, 4).toUpperCase();
        return `PRD-${itemCode}-${date}-${batchId || 'BATCH'}-${boxNumber}`;
    }

    /**
     * 生成客供物料二维码
     * @param {string} itemCode 物料编码
     * @param {string} batchId 批次ID
     * @param {string} customerCode 客户代码
     * @returns {string} 二维码
     */
    generateCustomerSuppliedQRCode(itemCode, batchId, customerCode) {
        const timestamp = Date.now().toString().slice(-6);
        const serial = Math.random().toString(36).substr(2, 4).toUpperCase();
        return `CUST-${customerCode || 'CUST'}-${itemCode}-${batchId || timestamp}-${serial}`;
    }

    /**
     * 验证二维码格式
     * @param {string} qrCode 二维码
     * @returns {Object} 验证结果
     */
    validateQRCodeFormat(qrCode) {
        const errors = [];
        
        if (!qrCode || typeof qrCode !== 'string') {
            errors.push('二维码不能为空');
            return { isValid: false, errors, message: '二维码格式错误' };
        }
        
        // 物料二维码格式: MAT-{供应商代码}-{物料编码}-{批次号}-{序列号}
        if (qrCode.startsWith('MAT-')) {
            if (!/^MAT-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+$/.test(qrCode)) {
                errors.push('物料二维码格式不正确');
            }
        }
        // 成品二维码格式: PRD-{产品编码}-{生产日期}-{批次号}-{箱号}
        else if (qrCode.startsWith('PRD-')) {
            if (!/^PRD-[A-Z0-9]+-\d{8}-[A-Z0-9]+-[A-Z0-9]+$/.test(qrCode)) {
                errors.push('成品二维码格式不正确');
            }
        }
        // 客供物料二维码格式: CUST-{客户代码}-{物料编码}-{批次号}-{序列号}
        else if (qrCode.startsWith('CUST-')) {
            if (!/^CUST-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+-[A-Z0-9]+$/.test(qrCode)) {
                errors.push('客供物料二维码格式不正确');
            }
        }
        else {
            errors.push('不支持的二维码类型');
        }
        
        return {
            isValid: errors.length === 0,
            errors,
            message: errors.length === 0 ? '二维码格式正确' : '二维码格式错误'
        };
    }

    /**
     * 解析二维码信息
     * @param {string} qrCode 二维码
     * @returns {Object} 解析结果
     */
    parseQRCode(qrCode) {
        const parts = qrCode.split('-');
        
        if (parts[0] === 'MAT') {
            return {
                type: 'material',
                supplierCode: parts[1],
                itemCode: parts[2],
                batchNumber: parts[3],
                serialNumber: parts[4]
            };
        } else if (parts[0] === 'PRD') {
            return {
                type: 'product',
                itemCode: parts[1],
                productionDate: parts[2],
                batchNumber: parts[3],
                boxNumber: parts[4]
            };
        } else if (parts[0] === 'CUST') {
            return {
                type: 'customer_supplied',
                customerCode: parts[1],
                itemCode: parts[2],
                batchNumber: parts[3],
                serialNumber: parts[4]
            };
        }
        
        return { type: 'unknown' };
    }
}

module.exports = QRCodeService;
