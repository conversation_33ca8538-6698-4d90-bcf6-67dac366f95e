/**
 * 仓库管理数据验证器
 * 验证请求数据的有效性
 */

class WarehouseValidator {
    /**
     * 验证入库数据
     * @param {Object} data 入库数据
     * @returns {Object} 验证结果
     */
    validateInbound(data) {
        const errors = [];
        
        // 必填字段验证
        if (!data.inboundType) {
            errors.push('入库类型不能为空');
        } else if (!['purchase', 'production', 'return', 'transfer'].includes(data.inboundType)) {
            errors.push('入库类型无效');
        }
        
        if (!data.itemType) {
            errors.push('物料类型不能为空');
        } else if (!['material', 'product'].includes(data.itemType)) {
            errors.push('物料类型无效');
        }
        
        if (!data.itemId) {
            errors.push('物料ID不能为空');
        }
        
        if (!data.itemCode) {
            errors.push('物料编码不能为空');
        }
        
        if (!data.itemName) {
            errors.push('物料名称不能为空');
        }
        
        if (!data.locationId) {
            errors.push('仓库位置不能为空');
        }
        
        if (!data.quantity || data.quantity <= 0) {
            errors.push('入库数量必须大于0');
        }
        
        if (data.quantity && !Number.isInteger(data.quantity)) {
            errors.push('入库数量必须为整数');
        }
        
        // 可选字段验证
        if (data.unit && typeof data.unit !== 'string') {
            errors.push('单位必须为字符串');
        }
        
        if (data.qrCodes && !Array.isArray(data.qrCodes)) {
            errors.push('二维码列表必须为数组');
        }
        
        if (data.notes && typeof data.notes !== 'string') {
            errors.push('备注必须为字符串');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 验证出库数据
     * @param {Object} data 出库数据
     * @returns {Object} 验证结果
     */
    validateOutbound(data) {
        const errors = [];
        
        // 必填字段验证
        if (!data.outboundType) {
            errors.push('出库类型不能为空');
        } else if (!['production', 'sale', 'return', 'transfer'].includes(data.outboundType)) {
            errors.push('出库类型无效');
        }
        
        if (!data.itemType) {
            errors.push('物料类型不能为空');
        } else if (!['material', 'product'].includes(data.itemType)) {
            errors.push('物料类型无效');
        }
        
        if (!data.itemId) {
            errors.push('物料ID不能为空');
        }
        
        if (!data.itemCode) {
            errors.push('物料编码不能为空');
        }
        
        if (!data.itemName) {
            errors.push('物料名称不能为空');
        }
        
        if (!data.locationId) {
            errors.push('仓库位置不能为空');
        }
        
        if (!data.quantity || data.quantity <= 0) {
            errors.push('出库数量必须大于0');
        }
        
        if (data.quantity && !Number.isInteger(data.quantity)) {
            errors.push('出库数量必须为整数');
        }
        
        // 可选字段验证
        if (data.outboundReason && typeof data.outboundReason !== 'string') {
            errors.push('出库原因必须为字符串');
        }
        
        if (data.unit && typeof data.unit !== 'string') {
            errors.push('单位必须为字符串');
        }
        
        if (data.destination && typeof data.destination !== 'string') {
            errors.push('目的地必须为字符串');
        }
        
        if (data.qrCodes && !Array.isArray(data.qrCodes)) {
            errors.push('二维码列表必须为数组');
        }
        
        if (data.notes && typeof data.notes !== 'string') {
            errors.push('备注必须为字符串');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 验证物料数据
     * @param {Object} data 物料数据
     * @returns {Object} 验证结果
     */
    validateMaterial(data) {
        const errors = [];
        
        // 必填字段验证
        if (!data.code) {
            errors.push('物料编码不能为空');
        } else if (!/^[A-Z0-9-_]+$/.test(data.code)) {
            errors.push('物料编码只能包含大写字母、数字、连字符和下划线');
        }
        
        if (!data.name) {
            errors.push('物料名称不能为空');
        } else if (data.name.length > 100) {
            errors.push('物料名称长度不能超过100个字符');
        }
        
        if (!data.category) {
            errors.push('物料分类不能为空');
        } else if (!['raw_material', 'component', 'packaging'].includes(data.category)) {
            errors.push('物料分类无效');
        }
        
        // 可选字段验证
        if (data.unit && typeof data.unit !== 'string') {
            errors.push('单位必须为字符串');
        }
        
        if (data.safetyStock && (!Number.isInteger(data.safetyStock) || data.safetyStock < 0)) {
            errors.push('安全库存必须为非负整数');
        }
        
        if (data.maxStock && (!Number.isInteger(data.maxStock) || data.maxStock < 0)) {
            errors.push('最大库存必须为非负整数');
        }
        
        if (data.leadTime && (!Number.isInteger(data.leadTime) || data.leadTime < 0)) {
            errors.push('采购周期必须为非负整数');
        }
        
        if (data.specifications && typeof data.specifications !== 'object') {
            errors.push('规格参数必须为对象');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 验证成品数据
     * @param {Object} data 成品数据
     * @returns {Object} 验证结果
     */
    validateProduct(data) {
        const errors = [];
        
        // 必填字段验证
        if (!data.code) {
            errors.push('成品编码不能为空');
        } else if (!/^[A-Z0-9-_]+$/.test(data.code)) {
            errors.push('成品编码只能包含大写字母、数字、连字符和下划线');
        }
        
        if (!data.name) {
            errors.push('成品名称不能为空');
        } else if (data.name.length > 100) {
            errors.push('成品名称长度不能超过100个字符');
        }
        
        // 可选字段验证
        if (data.category && typeof data.category !== 'string') {
            errors.push('成品分类必须为字符串');
        }
        
        if (data.unit && typeof data.unit !== 'string') {
            errors.push('单位必须为字符串');
        }
        
        if (data.packageSpecs && typeof data.packageSpecs !== 'object') {
            errors.push('包装规格必须为对象');
        }
        
        if (data.specifications && typeof data.specifications !== 'object') {
            errors.push('规格参数必须为对象');
        }
        
        if (data.safetyStock && (!Number.isInteger(data.safetyStock) || data.safetyStock < 0)) {
            errors.push('安全库存必须为非负整数');
        }
        
        if (data.maxStock && (!Number.isInteger(data.maxStock) || data.maxStock < 0)) {
            errors.push('最大库存必须为非负整数');
        }
        
        if (data.shelfLife && (!Number.isInteger(data.shelfLife) || data.shelfLife < 0)) {
            errors.push('保质期必须为非负整数');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 验证退料数据
     * @param {Object} data 退料数据
     * @returns {Object} 验证结果
     */
    validateReturn(data) {
        const errors = [];
        
        // 必填字段验证
        if (!data.returnType) {
            errors.push('退料类型不能为空');
        } else if (!['workshop_return', 'supplier_return', 'customer_return'].includes(data.returnType)) {
            errors.push('退料类型无效');
        }
        
        if (!data.returnReason) {
            errors.push('退料原因不能为空');
        } else if (!['excess', 'quality_issue', 'specification_mismatch', 'quantity_error', 'other'].includes(data.returnReason)) {
            errors.push('退料原因无效');
        }
        
        if (!data.itemType) {
            errors.push('物料类型不能为空');
        } else if (!['material', 'product'].includes(data.itemType)) {
            errors.push('物料类型无效');
        }
        
        if (!data.itemId) {
            errors.push('物料ID不能为空');
        }
        
        if (!data.itemCode) {
            errors.push('物料编码不能为空');
        }
        
        if (!data.itemName) {
            errors.push('物料名称不能为空');
        }
        
        if (!data.locationId) {
            errors.push('仓库位置不能为空');
        }
        
        if (!data.quantity || data.quantity <= 0) {
            errors.push('退料数量必须大于0');
        }
        
        if (data.quantity && !Number.isInteger(data.quantity)) {
            errors.push('退料数量必须为整数');
        }
        
        // 可选字段验证
        if (data.qualityCheckResult && typeof data.qualityCheckResult !== 'string') {
            errors.push('质量检查结果必须为字符串');
        }
        
        if (data.notes && typeof data.notes !== 'string') {
            errors.push('备注必须为字符串');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 验证库存调整数据
     * @param {Object} data 库存调整数据
     * @returns {Object} 验证结果
     */
    validateAdjustment(data) {
        const errors = [];
        
        // 必填字段验证
        if (!data.adjustmentType) {
            errors.push('调整类型不能为空');
        } else if (!['increase', 'decrease', 'correction'].includes(data.adjustmentType)) {
            errors.push('调整类型无效');
        }
        
        if (!data.adjustmentReason) {
            errors.push('调整原因不能为空');
        } else if (!['inventory_check', 'damage', 'loss', 'found', 'system_error'].includes(data.adjustmentReason)) {
            errors.push('调整原因无效');
        }
        
        if (!data.itemType) {
            errors.push('物料类型不能为空');
        } else if (!['material', 'product'].includes(data.itemType)) {
            errors.push('物料类型无效');
        }
        
        if (!data.itemId) {
            errors.push('物料ID不能为空');
        }
        
        if (!data.locationId) {
            errors.push('仓库位置不能为空');
        }
        
        if (data.quantityBefore === undefined || data.quantityBefore < 0) {
            errors.push('调整前数量不能为空且必须为非负数');
        }
        
        if (data.quantityAfter === undefined || data.quantityAfter < 0) {
            errors.push('调整后数量不能为空且必须为非负数');
        }
        
        if (!Number.isInteger(data.quantityBefore)) {
            errors.push('调整前数量必须为整数');
        }
        
        if (!Number.isInteger(data.quantityAfter)) {
            errors.push('调整后数量必须为整数');
        }
        
        return {
            isValid: errors.length === 0,
            errors
        };
    }
}

module.exports = WarehouseValidator;
