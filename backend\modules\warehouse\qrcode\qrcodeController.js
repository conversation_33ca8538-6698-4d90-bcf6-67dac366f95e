/**
 * 二维码控制器
 * 处理二维码相关的HTTP请求
 */

const QRCodeService = require('./qrcodeService');
const logger = require('../../../utils/logger');

class QRCodeController {
    constructor(db) {
        this.qrcodeService = new QRCodeService(db);
    }

    /**
     * 生成二维码
     * POST /api/warehouse/qrcode/generate
     */
    async generateQRCode(req, res) {
        try {
            const { itemType, itemId, itemCode, itemName, batchId, supplierCode, qrType } = req.body;
            
            // 验证必填字段
            if (!itemType || !itemId || !itemCode || !qrType) {
                return res.status(400).json({
                    success: false,
                    message: '缺少必填字段',
                    required: ['itemType', 'itemId', 'itemCode', 'qrType']
                });
            }
            
            const result = await this.qrcodeService.generateQRCode({
                itemType,
                itemId,
                itemCode,
                itemName,
                batchId,
                supplierCode,
                qrType
            });
            
            res.json(result);
            
        } catch (error) {
            logger.error('生成二维码失败:', error);
            res.status(500).json({
                success: false,
                message: '生成二维码失败',
                error: error.message
            });
        }
    }

    /**
     * 批量生成二维码
     * POST /api/warehouse/qrcode/generate-batch
     */
    async generateBatchQRCodes(req, res) {
        try {
            const { itemType, itemId, itemCode, itemName, batchId, supplierCode, qrType, quantity } = req.body;
            
            // 验证必填字段
            if (!itemType || !itemId || !itemCode || !qrType || !quantity) {
                return res.status(400).json({
                    success: false,
                    message: '缺少必填字段',
                    required: ['itemType', 'itemId', 'itemCode', 'qrType', 'quantity']
                });
            }
            
            if (quantity <= 0 || quantity > 1000) {
                return res.status(400).json({
                    success: false,
                    message: '数量必须在1-1000之间'
                });
            }
            
            const result = await this.qrcodeService.generateBatchQRCodes({
                itemType,
                itemId,
                itemCode,
                itemName,
                batchId,
                supplierCode,
                qrType,
                quantity
            });
            
            res.json(result);
            
        } catch (error) {
            logger.error('批量生成二维码失败:', error);
            res.status(500).json({
                success: false,
                message: '批量生成二维码失败',
                error: error.message
            });
        }
    }

    /**
     * 验证二维码
     * POST /api/warehouse/qrcode/validate
     */
    async validateQRCode(req, res) {
        try {
            const { qrCode, itemCode, operation } = req.body;
            
            if (!qrCode) {
                return res.status(400).json({
                    success: false,
                    message: '二维码不能为空'
                });
            }
            
            const result = await this.qrcodeService.validateQRCode(qrCode, {
                itemCode,
                operation
            });
            
            res.json(result);
            
        } catch (error) {
            logger.error('验证二维码失败:', error);
            res.status(500).json({
                success: false,
                message: '验证二维码失败',
                error: error.message
            });
        }
    }

    /**
     * 批量验证二维码
     * POST /api/warehouse/qrcode/validate-batch
     */
    async validateBatchQRCodes(req, res) {
        try {
            const { qrCodes, itemCode, operation } = req.body;
            
            if (!qrCodes || !Array.isArray(qrCodes) || qrCodes.length === 0) {
                return res.status(400).json({
                    success: false,
                    message: '二维码列表不能为空'
                });
            }
            
            if (qrCodes.length > 100) {
                return res.status(400).json({
                    success: false,
                    message: '单次最多验证100个二维码'
                });
            }
            
            const results = [];
            let validCount = 0;
            let invalidCount = 0;
            
            for (const qrCode of qrCodes) {
                try {
                    const result = await this.qrcodeService.validateQRCode(qrCode, {
                        itemCode,
                        operation
                    });
                    
                    results.push({
                        qrCode,
                        ...result
                    });
                    
                    if (result.isValid) {
                        validCount++;
                    } else {
                        invalidCount++;
                    }
                    
                } catch (error) {
                    results.push({
                        qrCode,
                        success: false,
                        isValid: false,
                        message: error.message
                    });
                    invalidCount++;
                }
            }
            
            res.json({
                success: true,
                results,
                summary: {
                    total: qrCodes.length,
                    valid: validCount,
                    invalid: invalidCount
                },
                message: `批量验证完成，有效${validCount}个，无效${invalidCount}个`
            });
            
        } catch (error) {
            logger.error('批量验证二维码失败:', error);
            res.status(500).json({
                success: false,
                message: '批量验证二维码失败',
                error: error.message
            });
        }
    }

    /**
     * 解析二维码信息
     * POST /api/warehouse/qrcode/parse
     */
    async parseQRCode(req, res) {
        try {
            const { qrCode } = req.body;
            
            if (!qrCode) {
                return res.status(400).json({
                    success: false,
                    message: '二维码不能为空'
                });
            }
            
            // 格式验证
            const formatValidation = this.qrcodeService.validateQRCodeFormat(qrCode);
            if (!formatValidation.isValid) {
                return res.json({
                    success: false,
                    message: formatValidation.message,
                    errors: formatValidation.errors
                });
            }
            
            // 解析信息
            const parsedInfo = this.qrcodeService.parseQRCode(qrCode);
            
            res.json({
                success: true,
                qrCode,
                parsedInfo,
                message: '二维码解析成功'
            });
            
        } catch (error) {
            logger.error('解析二维码失败:', error);
            res.status(500).json({
                success: false,
                message: '解析二维码失败',
                error: error.message
            });
        }
    }

    /**
     * 获取二维码历史记录
     * GET /api/warehouse/qrcode/history
     */
    async getQRCodeHistory(req, res) {
        try {
            const { qrCode, itemCode, page = 1, limit = 20 } = req.query;
            
            let query = 'SELECT * FROM warehouse_qrcodes WHERE 1=1';
            const params = [];
            
            if (qrCode) {
                query += ' AND qr_code = ?';
                params.push(qrCode);
            }
            
            if (itemCode) {
                query += ' AND item_code LIKE ?';
                params.push(`%${itemCode}%`);
            }
            
            query += ' ORDER BY created_at DESC';
            
            // 分页
            const offset = (page - 1) * limit;
            query += ` LIMIT ? OFFSET ?`;
            params.push(parseInt(limit), offset);
            
            const qrCodes = this.qrcodeService.db.prepare(query).all(...params);
            
            // 获取总数
            let countQuery = 'SELECT COUNT(*) as total FROM warehouse_qrcodes WHERE 1=1';
            const countParams = [];
            
            if (qrCode) {
                countQuery += ' AND qr_code = ?';
                countParams.push(qrCode);
            }
            
            if (itemCode) {
                countQuery += ' AND item_code LIKE ?';
                countParams.push(`%${itemCode}%`);
            }
            
            const { total } = this.qrcodeService.db.prepare(countQuery).get(...countParams);
            
            res.json({
                success: true,
                data: {
                    qrCodes,
                    pagination: {
                        page: parseInt(page),
                        limit: parseInt(limit),
                        total,
                        pages: Math.ceil(total / limit)
                    }
                }
            });
            
        } catch (error) {
            logger.error('获取二维码历史记录失败:', error);
            res.status(500).json({
                success: false,
                message: '获取二维码历史记录失败',
                error: error.message
            });
        }
    }

    /**
     * 标记二维码为已使用
     * POST /api/warehouse/qrcode/mark-used
     */
    async markQRCodeUsed(req, res) {
        try {
            const { qrCode } = req.body;
            
            if (!qrCode) {
                return res.status(400).json({
                    success: false,
                    message: '二维码不能为空'
                });
            }
            
            const result = await this.qrcodeService.markQRCodeUsed(qrCode);
            res.json(result);
            
        } catch (error) {
            logger.error('标记二维码失败:', error);
            res.status(500).json({
                success: false,
                message: '标记二维码失败',
                error: error.message
            });
        }
    }

    /**
     * 获取二维码统计信息
     * GET /api/warehouse/qrcode/stats
     */
    async getQRCodeStats(req, res) {
        try {
            const stats = this.qrcodeService.db.prepare(`
                SELECT 
                    qr_type,
                    COUNT(*) as total_count,
                    SUM(CASE WHEN is_used = 1 THEN 1 ELSE 0 END) as used_count,
                    SUM(CASE WHEN is_used = 0 THEN 1 ELSE 0 END) as unused_count
                FROM warehouse_qrcodes 
                GROUP BY qr_type
            `).all();
            
            const totalStats = this.qrcodeService.db.prepare(`
                SELECT 
                    COUNT(*) as total_count,
                    SUM(CASE WHEN is_used = 1 THEN 1 ELSE 0 END) as used_count,
                    SUM(CASE WHEN is_used = 0 THEN 1 ELSE 0 END) as unused_count,
                    COUNT(DISTINCT item_id) as unique_items
                FROM warehouse_qrcodes
            `).get();
            
            res.json({
                success: true,
                data: {
                    byType: stats,
                    total: totalStats
                }
            });
            
        } catch (error) {
            logger.error('获取二维码统计信息失败:', error);
            res.status(500).json({
                success: false,
                message: '获取二维码统计信息失败',
                error: error.message
            });
        }
    }
}

module.exports = QRCodeController;
